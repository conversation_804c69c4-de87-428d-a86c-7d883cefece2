def heatmap_analysis(request):
    """����ͼ��������Ӱ������"""
    from django.db.models import Avg, Min, Max
    
    username = request.session['username'].get('username')
    useravatar = request.session['username'].get('avatar')
    
    # ��ȡ���з�Դ����
    houses = House.objects.all()
    
    # ��ȡ�����۸��ϵ����
    area_price_data = []
    for house in houses:
        # �����쳣ֵ
        if house.price < 10000 and house.area > 0 and house.area < 300:
            area_price_data.append([house.area, house.price])
    
    # ��ȡ������۸��ϵ����
    cities = list(House.objects.values_list('city', flat=True).distinct())
    city_type_price = []
    
    # ��ȡ���ж��صķ�Դ����
    types = list(House.objects.values_list('type', flat=True).distinct())
    
    # ����ÿ������ÿ�����͵�ƽ���۸�
    for city_idx, city in enumerate(cities):
        for type_idx, house_type in enumerate(types):
            avg_price = House.objects.filter(city=city, type=house_type).aggregate(avg=Avg('price'))['avg']
            if avg_price:  # ȷ��������
                city_type_price.append([city_idx, type_idx, avg_price])
    
    # ����볯��Ĺ�ϵ�Լ۸��Ӱ��
    directs = list(House.objects.values_list('direct', flat=True).distinct())
    area_direct_price = []
    
    # ��area��Ϊ10������
    area_ranges = []
    min_area = House.objects.aggregate(min_area=Min('area'))['min_area'] or 0
    max_area = House.objects.aggregate(max_area=Max('area'))['max_area'] or 100
    step = (max_area - min_area) / 10
    
    for i in range(10):
        area_ranges.append([min_area + i * step, min_area + (i + 1) * step])
    
    # ����ÿ���������ͳ�����ϵ�ƽ���۸�
    for area_idx, area_range in enumerate(area_ranges):
        for direct_idx, direct in enumerate(directs):
            # ����������ڸó����ƽ���۸�
            avg_price = House.objects.filter(
                area__gte=area_range[0], 
                area__lt=area_range[1], 
                direct=direct
            ).aggregate(avg=Avg('price'))['avg']
            
            if avg_price:  # ȷ��������
                area_direct_price.append([area_idx, direct_idx, avg_price])
    
    context = {
        'username': username,
        'useravatar': useravatar,
        'area_price_data': area_price_data,
        'city_type_price': city_type_price,
        'area_direct_price': area_direct_price,
        'cities': cities,
        'types': types,
        'directs': directs,
        'area_ranges': area_ranges
    }
    
    return render(request, 'heatmap_analysis.html', context) 