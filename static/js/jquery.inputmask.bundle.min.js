/*
 Input Mask plugin for jquery
 http://github.com/RobinHerbots/jquery.inputmask
 Copyright (c) 2010 - 2013 <PERSON>
 Licensed under the MIT license (http://www.opensource.org/licenses/mit-license.php)
 Version: 2.4.0
*/
(function(e){void 0===e.fn.inputmask&&(e.inputmask={defaults:{placeholder:"_",optionalmarker:{start:"[",end:"]"},quantifiermarker:{start:"{",end:"}"},groupmarker:{start:"(",end:")"},escapeChar:"\\",mask:null,oncomplete:e.noop,onincomplete:e.noop,oncleared:e.noop,repeat:0,greedy:!0,autoUnmask:!1,clearMaskOnLostFocus:!0,insertMode:!0,clearIncomplete:!1,aliases:{},onKeyUp:e.noop,onKeyDown:e.noop,showMaskOnFocus:!0,showMaskOnHover:!0,onKeyValidation:e.noop,skipOptionalPartCharacter:" ",showTooltip:!1,
numericInput:!1,isNumeric:!1,radixPoint:"",skipRadixDance:!1,rightAlignNumerics:!0,definitions:{9:{validator:"[0-9]",cardinality:1},a:{validator:"[A-Za-z\u0410-\u044f\u0401\u0451]",cardinality:1},"*":{validator:"[A-Za-z\u0410-\u044f\u0401\u04510-9]",cardinality:1}},keyCode:{ALT:18,BACKSPACE:8,CAPS_LOCK:20,COMMA:188,COMMAND:91,COMMAND_LEFT:91,COMMAND_RIGHT:93,CONTROL:17,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,INSERT:45,LEFT:37,MENU:93,NUMPAD_ADD:107,NUMPAD_DECIMAL:110,NUMPAD_DIVIDE:111,
NUMPAD_ENTER:108,NUMPAD_MULTIPLY:106,NUMPAD_SUBTRACT:109,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SHIFT:16,SPACE:32,TAB:9,UP:38,WINDOWS:91},ignorables:[8,9,13,19,27,33,34,35,36,37,38,39,40,45,46,93,112,113,114,115,116,117,118,119,120,121,122,123],getMaskLength:function(a,e,d,f,b){b=a.length;e||("*"==d?b=f.length+1:1<d&&(b+=a.length*(d-1)));return b}},escapeRegex:function(a){return a.replace(RegExp("(\\/|\\.|\\*|\\+|\\?|\\||\\(|\\)|\\[|\\]|\\{|\\}|\\\\)","gim"),"\\$1")}},e.fn.inputmask=function(a,
c){function d(a){var b=document.createElement("input");a="on"+a;var g=a in b;g||(b.setAttribute(a,"return;"),g="function"==typeof b[a]);return g}function f(a,b){var d=g.aliases[a];return d?(d.alias&&f(d.alias),e.extend(!0,g,d),e.extend(!0,g,b),!0):!1}function b(a){g.numericInput&&(a=a.split("").reverse().join(""));var b=!1,d=0,c=g.greedy,f=g.repeat;"*"==f&&(c=!1);1==a.length&&!1==c&&0!=f&&(g.placeholder="");a=e.map(a.split(""),function(a,e){var c=[];if(a==g.escapeChar)b=!0;else if(a!=g.optionalmarker.start&&
a!=g.optionalmarker.end||b){var f=g.definitions[a];if(f&&!b)for(var h=0;h<f.cardinality;h++)c.push(A(d+h));else c.push(a),b=!1;d+=c.length;return c}});for(var h=a.slice(),r=1;r<f&&c;r++)h=h.concat(a.slice());return{mask:h,repeat:f,greedy:c}}function h(a){g.numericInput&&(a=a.split("").reverse().join(""));var b=!1,d=!1,c=!1;return e.map(a.split(""),function(a,e){var f=[];if(a==g.escapeChar)d=!0;else{if(a!=g.optionalmarker.start||d){if(a!=g.optionalmarker.end||d){var h=g.definitions[a];if(h&&!d){for(var x=
h.prevalidator,k=x?x.length:0,n=1;n<h.cardinality;n++){var s=k>=n?x[n-1]:[],l=s.validator,s=s.cardinality;f.push({fn:l?"string"==typeof l?RegExp(l):new function(){this.test=l}:/./,cardinality:s?s:1,optionality:b,newBlockMarker:!0==b?c:!1,offset:0,casing:h.casing,def:h.definitionSymbol||a});!0==b&&(c=!1)}f.push({fn:h.validator?"string"==typeof h.validator?RegExp(h.validator):new function(){this.test=h.validator}:/./,cardinality:h.cardinality,optionality:b,newBlockMarker:c,offset:0,casing:h.casing,
def:h.definitionSymbol||a})}else f.push({fn:null,cardinality:0,optionality:b,newBlockMarker:c,offset:0,casing:null,def:a}),d=!1;c=!1;return f}b=!1}else b=!0;c=!0}})}function k(){function a(b){var d=b.length;for(i=0;i<d&&b.charAt(i)!=g.optionalmarker.start;i++);var c=[b.substring(0,i)];i<d&&c.push(b.substring(i+1,d));return c}function d(k,r,l){var p=0,z=0,n=r.length;for(i=0;i<n&&!(r.charAt(i)==g.optionalmarker.start&&p++,r.charAt(i)==g.optionalmarker.end&&z++,0<p&&p==z);i++);p=[r.substring(0,i)];i<
n&&p.push(r.substring(i+1,n));z=a(p[0]);1<z.length?(r=k+z[0]+(g.optionalmarker.start+z[1]+g.optionalmarker.end)+(1<p.length?p[1]:""),-1==e.inArray(r,f)&&(f.push(r),n=b(r),c.push({mask:r,_buffer:n.mask,buffer:n.mask.slice(),tests:h(r),lastValidPosition:-1,greedy:n.greedy,repeat:n.repeat,metadata:l})),r=k+z[0]+(1<p.length?p[1]:""),-1==e.inArray(r,f)&&(f.push(r),n=b(r),c.push({mask:r,_buffer:n.mask,buffer:n.mask.slice(),tests:h(r),lastValidPosition:-1,greedy:n.greedy,repeat:n.repeat,metadata:l})),1<
a(z[1]).length&&d(k+z[0],z[1]+p[1],l),1<p.length&&1<a(p[1]).length&&(d(k+z[0]+(g.optionalmarker.start+z[1]+g.optionalmarker.end),p[1],l),d(k+z[0],p[1],l))):(r=k+p,-1==e.inArray(r,f)&&(f.push(r),n=b(r),c.push({mask:r,_buffer:n.mask,buffer:n.mask.slice(),tests:h(r),lastValidPosition:-1,greedy:n.greedy,repeat:n.repeat,metadata:l})))}var c=[],f=[],k=[];e.isFunction(g.mask)&&(g.mask=g.mask.call(this,g));e.isArray(g.mask)?e.each(g.mask,function(a,b){void 0!=b.mask?d("",b.mask.toString(),b):d("",b.toString())}):
d("",g.mask.toString());(function(a){function b(){this.matches=[];this.isQuantifier=this.isOptional=this.isGroup=!1}var d=/(?:[?*+]|\{[0-9]+(?:,[0-9]*)?\})\??|[^.?*+^${[]()|\\]+|./g,c=new b,e,f=[];for(k=[];e=d.exec(a);)switch(e=e[0],e.charAt(0)){case g.optionalmarker.end:case g.groupmarker.end:e=f.pop();0<f.length?f[f.length-1].matches.push(e):(k.push(e),c=new b);break;case g.optionalmarker.start:!c.isGroup&&0<c.matches.length&&k.push(c);c=new b;c.isOptional=!0;f.push(c);break;case g.groupmarker.start:!c.isGroup&&
0<c.matches.length&&k.push(c);c=new b;c.isGroup=!0;f.push(c);break;case g.quantifiermarker.start:var h=new b;h.isQuantifier=!0;h.matches.push(e);0<f.length?f[f.length-1].matches.push(h):c.matches.push(h);break;default:0<f.length?f[f.length-1].matches.push(e):c.matches.push(e)}0<c.matches.length&&k.push(c);return k})(g.mask);return g.greedy?c:c.sort(function(a,b){return a.mask.length-b.mask.length})}function A(a){return g.placeholder.charAt(a%g.placeholder.length)}function q(a,b){function c(){return a[b]}
function d(){return c().tests}function f(){return c()._buffer}function h(){return c().buffer}function k(d,f,G){function l(a,c,b,f){for(var d=m(a),h=b?1:0,e="",I=c.buffer,T=c.tests[d].cardinality;T>h;T--)e+=J(I,d-(T-1));b&&(e+=b);return null!=c.tests[d].fn?c.tests[d].fn.test(e,I,a,f,g):b==J(c._buffer,a,!0)||b==g.skipOptionalPartCharacter?{refresh:!0,c:J(c._buffer,a,!0),pos:a}:!1}if(G=!0===G){var D=l(d,c(),f,G);!0===D&&(D={pos:d});return D}var F=[],D=!1,r=b,w=h().slice(),t=c().lastValidPosition;q(d);
var v=[];e.each(a,function(a,g){if("object"==typeof g){b=a;var e=d,k=c().lastValidPosition,m;if(k==t){if(1<e-t)for(k=-1==k?0:k;k<e&&(m=l(k,c(),w[k],!0),!1!==m);k++)u(h(),k,w[k],!0),!0===m&&(m={pos:k}),m=m.pos||k,c().lastValidPosition<m&&(c().lastValidPosition=m);if(!p(e)&&!l(e,c(),f,G)){k=s(e)-e;for(m=0;m<k&&!1===l(++e,c(),f,G);m++);v.push(b)}}(c().lastValidPosition>=t||b==r)&&0<=e&&e<n()&&(D=l(e,c(),f,G),!1!==D&&(!0===D&&(D={pos:e}),m=D.pos||e,c().lastValidPosition<m&&(c().lastValidPosition=m)),
F.push({activeMasksetIndex:a,result:D}))}});b=r;return function(c,b){var h=!1;e.each(b,function(a,b){if(h=-1==e.inArray(b.activeMasksetIndex,c)&&!1!==b.result)return!1});if(h)b=e.map(b,function(b,d){if(-1==e.inArray(b.activeMasksetIndex,c))return b;a[b.activeMasksetIndex].lastValidPosition=t});else{var g=-1,k=-1;e.each(b,function(a,b){-1!=e.inArray(b.activeMasksetIndex,c)&&!1!==b.result&(-1==g||g>b.result.pos)&&(g=b.result.pos,k=b.activeMasksetIndex)});b=e.map(b,function(b,h){if(-1!=e.inArray(b.activeMasksetIndex,
c)){if(b.result.pos==g)return b;if(!1!==b.result){for(var I=d;I<g&&(rsltValid=l(I,a[b.activeMasksetIndex],a[k].buffer[I],!0),!1!==rsltValid);I++)u(a[b.activeMasksetIndex].buffer,I,a[k].buffer[I],!0),a[b.activeMasksetIndex].lastValidPosition=I;rsltValid=l(g,a[b.activeMasksetIndex],f,!0);!1!==rsltValid&&(u(a[b.activeMasksetIndex].buffer,g,f,!0),a[b.activeMasksetIndex].lastValidPosition=g);return b}}})}return b}(v,F)}function l(){var d=b,f={activeMasksetIndex:0,lastValidPosition:-1,next:-1};e.each(a,
function(a,d){"object"==typeof d&&(b=a,c().lastValidPosition>f.lastValidPosition?(f.activeMasksetIndex=a,f.lastValidPosition=c().lastValidPosition,f.next=s(c().lastValidPosition)):c().lastValidPosition==f.lastValidPosition&&(-1==f.next||f.next>s(c().lastValidPosition))&&(f.activeMasksetIndex=a,f.lastValidPosition=c().lastValidPosition,f.next=s(c().lastValidPosition)))});b=-1!=f.lastValidPosition&&a[d].lastValidPosition==f.lastValidPosition?d:f.activeMasksetIndex;d!=b&&(V(h(),s(f.lastValidPosition),
n()),c().writeOutBuffer=!0);t.data("_inputmask").activeMasksetIndex=b}function p(a){a=m(a);a=d()[a];return void 0!=a?a.fn:!1}function m(a){return a%d().length}function n(){return g.getMaskLength(f(),c().greedy,c().repeat,h(),g)}function s(a){var b=n();if(a>=b)return b;for(;++a<b&&!p(a););return a}function q(a){if(0>=a)return 0;for(;0<--a&&!p(a););return a}function u(a,b,c,f){f&&(b=P(a,b));f=d()[m(b)];var h=c;if(void 0!=h&&void 0!=f)switch(f.casing){case "upper":h=c.toUpperCase();break;case "lower":h=
c.toLowerCase()}a[b]=h}function J(a,b,c){c&&(b=P(a,b));return a[b]}function P(a,b){for(var c;void 0==a[b]&&a.length<n();)for(c=0;void 0!==f()[c];)a.push(f()[c++]);return b}function L(a,b,c){a._valueSet(b.join(""));void 0!=c&&w(a,c)}function V(a,b,c,d){for(var h=n();b<c&&b<h;b++)!0===d?p(b)||u(a,b,""):u(a,b,J(f().slice(),b,!0))}function Q(a,b){var c=m(b);u(a,b,J(f(),c))}function M(d,h,k,l){l=void 0!=l?l.slice():W(d._valueGet()).split("");e.each(a,function(a,b){"object"==typeof b&&(b.buffer=b._buffer.slice(),
b.lastValidPosition=-1,b.p=-1)});!0!==k&&(b=0);h&&d._valueSet("");n();e.each(l,function(a,b){var l=c().lastValidPosition,m=-1==l?a:s(l);(e.isArray(g.mask)||-1==e.inArray(b,f().slice(l+1,m)))&&e(d).trigger("_keypress",[!0,b.charCodeAt(0),h,k,a])});!0===k&&-1!=c().p&&(c().lastValidPosition=q(c().p))}function X(a){return e.inputmask.escapeRegex.call(this,a)}function W(a){return a.replace(RegExp("("+X(f().join(""))+")*$"),"")}function Y(a){var b=h(),c=b.slice(),f,e;for(e=c.length-1;0<=e;e--)if(f=m(e),
d()[f].optionality)if(p(e)&&k(e,b[e],!0))break;else c.pop();else break;L(a,c)}function Z(a,b){if(!d()||!0!==b&&a.hasClass("hasDatepicker"))return a[0]._valueGet();var c=e.map(h(),function(a,b){return p(b)&&k(b,a,!0)?a:null});return(B?c.reverse():c).join("")}function O(a){!B||"number"!=typeof a||g.greedy&&""==g.placeholder||(a=h().length-a);return a}function w(a,b,c){var f=a.jquery&&0<a.length?a[0]:a;if("number"==typeof b)b=O(b),c=O(c),e(a).is(":visible")&&(c="number"==typeof c?c:b,f.scrollLeft=f.scrollWidth,
!1==g.insertMode&&b==c&&c++,f.setSelectionRange?(f.selectionStart=b,f.selectionEnd=v?b:c):f.createTextRange&&(a=f.createTextRange(),a.collapse(!0),a.moveEnd("character",c),a.moveStart("character",b),a.select()));else{if(!e(a).is(":visible"))return{begin:0,end:0};f.setSelectionRange?(b=f.selectionStart,c=f.selectionEnd):document.selection&&document.selection.createRange&&(a=document.selection.createRange(),b=0-a.duplicate().moveStart("character",-1E5),c=b+a.text.length);b=O(b);c=O(c);return{begin:b,
end:c}}}function R(c){var d=!1,h=0,g=b;e.each(a,function(a,e){if("object"==typeof e){b=a;var g=q(n());if(e.lastValidPosition>=h&&e.lastValidPosition==g){for(var k=!0,l=0;l<=g;l++){var x=p(l),F=m(l);if(x&&(void 0==c[l]||c[l]==A(l))||!x&&c[l]!=f()[F]){k=!1;break}}if(d=d||k)return!1}h=e.lastValidPosition}});b=g;return d}var B=!1,E=h().join(""),t;this.unmaskedvalue=function(a,b){B=a.data("_inputmask").isRTL;return Z(a,b)};this.isComplete=function(a){return R(a)};this.mask=function(v){function P(a){a=
e._data(a).events;e.each(a,function(a,b){e.each(b,function(a,b){if("inputmask"==b.namespace&&"setvalue"!=b.type&&"_keypress"!=b.type){var c=b.handler;b.handler=function(a){if(this.readOnly||this.disabled)a.preventDefault;else return c.apply(this,arguments)}}})})}function G(a){var b;Object.getOwnPropertyDescriptor&&(b=Object.getOwnPropertyDescriptor(a,"value"));if(b&&b.get){if(!a._valueGet){var c=b.get,f=b.set;a._valueGet=function(){return B?c.call(this).split("").reverse().join(""):c.call(this)};
a._valueSet=function(a){f.call(this,B?a.split("").reverse().join(""):a)};Object.defineProperty(a,"value",{get:function(){var a=e(this),b=e(this).data("_inputmask"),f=b.masksets,d=b.activeMasksetIndex;return b&&b.opts.autoUnmask?a.inputmask("unmaskedvalue"):c.call(this)!=f[d]._buffer.join("")?c.call(this):""},set:function(a){f.call(this,a);e(this).triggerHandler("setvalue.inputmask")}})}}else if(document.__lookupGetter__&&a.__lookupGetter__("value"))a._valueGet||(c=a.__lookupGetter__("value"),f=a.__lookupSetter__("value"),
a._valueGet=function(){return B?c.call(this).split("").reverse().join(""):c.call(this)},a._valueSet=function(a){f.call(this,B?a.split("").reverse().join(""):a)},a.__defineGetter__("value",function(){var a=e(this),b=e(this).data("_inputmask"),f=b.masksets,d=b.activeMasksetIndex;return b&&b.opts.autoUnmask?a.inputmask("unmaskedvalue"):c.call(this)!=f[d]._buffer.join("")?c.call(this):""}),a.__defineSetter__("value",function(a){f.call(this,a);e(this).triggerHandler("setvalue.inputmask")}));else if(a._valueGet||
(a._valueGet=function(){return B?this.value.split("").reverse().join(""):this.value},a._valueSet=function(a){this.value=B?a.split("").reverse().join(""):a}),void 0==e.valHooks.text||!0!=e.valHooks.text.inputmaskpatch)c=e.valHooks.text&&e.valHooks.text.get?e.valHooks.text.get:function(){return this.value},f=e.valHooks.text&&e.valHooks.text.set?e.valHooks.text.set:function(a){return this.value=a},jQuery.extend(e.valHooks,{text:{get:function(a){var b=e(a);if(b.data("_inputmask")){if(b.data("_inputmask").opts.autoUnmask)return b.inputmask("unmaskedvalue");
a=c.call(a);b=b.data("_inputmask");return a!=b.masksets[b.activeMasksetIndex]._buffer.join("")?a:""}return c.call(a)},set:function(a,b){var c=e(a),d=f.call(a,b);c.data("_inputmask")&&c.triggerHandler("setvalue.inputmask");return d},inputmaskpatch:!0}})}function $(a,b,e,g){var l=h();if(!1!==g)for(;!p(a)&&0<=a-1;)a--;for(g=a;g<b&&g<n();g++)if(p(g)){Q(l,g);var x=s(g),F=J(l,x);if(F!=A(x))if(x<n()&&!1!==k(g,F,!0)&&d()[m(g)].def==d()[m(x)].def)u(l,g,J(l,x),!0),x<b&&Q(l,x);else if(p(g))break}else Q(l,g);
void 0!=e&&u(l,q(b),e);if(!1==c().greedy){b=W(l.join("")).split("");l.length=b.length;g=0;for(e=l.length;g<e;g++)l[g]=b[g];0==l.length&&(c().buffer=f().slice())}return a}function D(a,b,g,e){for(var l=h();a<=b&&a<n();a++)if(p(a)){var x=J(l,a,!0);u(l,a,g,!0);if(x!=A(a))if(g=s(a),g<n())if(!1!==k(g,x,!0)&&d()[m(a)].def==d()[m(g)].def)g=x;else if(p(g))break;else g=x;else break;else if(g=x,!0!==e)break}else Q(l,a);e=l.length;if(!1==c().greedy){g=W(l.join("")).split("");l.length=g.length;a=0;for(x=l.length;a<
x;a++)l[a]=g[a];0==l.length&&(c().buffer=f().slice())}return b-(e-l.length)}function da(b,f,d){if(g.numericInput||B){switch(f){case g.keyCode.BACKSPACE:f=g.keyCode.DELETE;break;case g.keyCode.DELETE:f=g.keyCode.BACKSPACE}if(B){var e=d.end;d.end=d.begin;d.begin=e}}e=!0;d.begin==d.end?(e=f==g.keyCode.BACKSPACE?d.begin-1:d.begin,g.isNumeric&&""!=g.radixPoint&&h()[e]==g.radixPoint&&(d.begin=h().length-1==e?d.begin:f==g.keyCode.BACKSPACE?e:s(e),d.end=d.begin),e=!1,f==g.keyCode.BACKSPACE?d.begin--:f==g.keyCode.DELETE&&
d.end++):1!=d.end-d.begin||g.insertMode||(e=!1,f==g.keyCode.BACKSPACE&&d.begin--);V(h(),d.begin,d.end);var k=n();if(!1==g.greedy)$(d.begin,k,void 0,!B&&f==g.keyCode.BACKSPACE&&!e);else{for(var l=d.begin,m=d.begin;m<d.end;m++)if(p(m)||!e)l=$(d.begin,k,void 0,!B&&f==g.keyCode.BACKSPACE&&!e);e||(d.begin=l)}f=s(-1);V(h(),d.begin,d.end,!0);M(b,!1,void 0==a[1],h());c().lastValidPosition<f?(c().lastValidPosition=-1,c().p=f):c().p=d.begin}function X(a){U=!1;var b=this,d=e(b),k=a.keyCode,m=w(b);k==g.keyCode.BACKSPACE||
k==g.keyCode.DELETE||N&&127==k||a.ctrlKey&&88==k?(a.preventDefault(),88==k&&(E=h().join("")),da(b,k,m),l(),L(b,h(),c().p),b._valueGet()==f().join("")&&d.trigger("cleared"),g.showTooltip&&d.prop("title",c().mask)):k==g.keyCode.END||k==g.keyCode.PAGE_DOWN?setTimeout(function(){var d=s(c().lastValidPosition);g.insertMode||d!=n()||a.shiftKey||d--;w(b,a.shiftKey?m.begin:d,d)},0):k==g.keyCode.HOME&&!a.shiftKey||k==g.keyCode.PAGE_UP?w(b,0,a.shiftKey?m.begin:0):k==g.keyCode.ESCAPE||90==k&&a.ctrlKey?(M(b,
!0,!1,E),d.click()):k!=g.keyCode.INSERT||a.shiftKey||a.ctrlKey?!1!=g.insertMode||a.shiftKey||(k==g.keyCode.RIGHT?setTimeout(function(){var a=w(b);w(b,a.begin)},0):k==g.keyCode.LEFT&&setTimeout(function(){var a=w(b);w(b,a.begin-1)},0)):(g.insertMode=!g.insertMode,w(b,g.insertMode||m.begin!=n()?m.begin:m.begin-1));d=w(b);!0===g.onKeyDown.call(this,a,h(),g)&&w(b,d.begin,d.end);aa=-1!=e.inArray(k,g.ignorables)}function ea(d,f,m,F,v,p){if(void 0==m&&U)return!1;U=!0;var t=e(this);d=d||window.event;m=m||
d.which||d.charCode||d.keyCode;if((!d.ctrlKey||!d.altKey)&&(d.ctrlKey||d.metaKey||aa)&&!0!==f)return!0;if(m){!0!==f&&46==m&&!1==d.shiftKey&&","==g.radixPoint&&(m=44);var y,N,z=String.fromCharCode(m);f?(m=v?p:c().lastValidPosition+1,y={begin:m,end:m}):y=w(this);p=B?1<y.begin-y.end||1==y.begin-y.end&&g.insertMode:1<y.end-y.begin||1==y.end-y.begin&&g.insertMode;var G=b;p&&(b=G,e.each(a,function(a,d){"object"==typeof d&&(b=a,c().undoBuffer=h().join(""))}),da(this,g.keyCode.DELETE,y),g.insertMode||e.each(a,
function(a,d){"object"==typeof d&&(b=a,D(y.begin,n(),A(y.begin),!0),c().lastValidPosition=s(c().lastValidPosition))}),b=G);var E=h().join("").indexOf(g.radixPoint);g.isNumeric&&!0!==f&&-1!=E&&(g.greedy&&y.begin<=E?(y.begin=q(y.begin),y.end=y.begin):z==g.radixPoint&&(y.begin=E,y.end=y.begin));var C=y.begin;m=k(C,z,v);!0===v&&(m=[{activeMasksetIndex:b,result:m}]);var H=-1;e.each(m,function(a,d){b=d.activeMasksetIndex;c().writeOutBuffer=!0;var f=d.result;if(!1!==f){var e=!1,k=h();!0!==f&&(e=f.refresh,
C=void 0!=f.pos?f.pos:C,z=void 0!=f.c?f.c:z);if(!0!==e){if(!0==g.insertMode){f=n();for(e=k.slice();J(e,f,!0)!=A(f)&&f>=C;)f=0==f?-1:q(f);f>=C?(D(C,k.length,z),k=c().lastValidPosition,f=s(k),f!=n()&&k>=C&&J(h(),f,!0)!=A(f)&&(c().lastValidPosition=f)):c().writeOutBuffer=!1}else u(k,C,z,!0);if(-1==H||H>s(C))H=s(C)}else!v&&(k=C<n()?C+1:C,-1==H||H>k)&&(H=k);H>c().p&&(c().p=H)}});!0!==v&&(b=G,l());if(!1!==F&&(e.each(m,function(a,c){if(c.activeMasksetIndex==b)return N=c,!1}),void 0!=N)){var K=this;setTimeout(function(){g.onKeyValidation.call(K,
N.result,g)},0);if(c().writeOutBuffer&&!1!==N.result){var M=h();F=f?void 0:g.numericInput?C>E?q(H):z==g.radixPoint?H-1:q(H-1):H;L(this,M,F);!0!==f&&setTimeout(function(){R(M)&&t.trigger("complete")},0)}else p&&(c().buffer=c().undoBuffer.split(""))}g.showTooltip&&t.prop("title",c().mask);d.preventDefault()}}function Z(a){var b=e(this),c=a.keyCode,d=h();g.onKeyUp.call(this,a,d,g);c==g.keyCode.TAB&&g.showMaskOnFocus&&(b.hasClass("focus.inputmask")&&0==this._valueGet().length?(d=f().slice(),L(this,d),
w(this,0),E=h().join("")):(L(this,d),w(this,0,n())))}t=e(v);if(t.is(":input")){t.data("_inputmask",{masksets:a,activeMasksetIndex:b,opts:g,isRTL:!1});g.showTooltip&&t.prop("title",c().mask);c().greedy=c().greedy?c().greedy:0==c().repeat;if(null!=t.attr("maxLength")){var K=t.prop("maxLength");-1<K&&e.each(a,function(a,b){"object"==typeof b&&"*"==b.repeat&&(b.repeat=K)});n()>K&&-1<K&&(K<f().length&&(f().length=K),!1==c().greedy&&(c().repeat=Math.round(K/f().length)),t.prop("maxLength",2*n()))}G(v);
var U=!1,aa=!1;g.numericInput&&(g.isNumeric=g.numericInput);("rtl"==v.dir||g.numericInput&&g.rightAlignNumerics||g.isNumeric&&g.rightAlignNumerics)&&t.css("text-align","right");if("rtl"==v.dir||g.numericInput){v.dir="ltr";t.removeAttr("dir");var ba=t.data("_inputmask");ba.isRTL=!0;t.data("_inputmask",ba);B=!0}t.unbind(".inputmask");t.removeClass("focus.inputmask");t.closest("form").bind("submit",function(){E!=h().join("")&&t.change()}).bind("reset",function(){setTimeout(function(){t.trigger("setvalue")},
0)});t.bind("mouseenter.inputmask",function(){!e(this).hasClass("focus.inputmask")&&g.showMaskOnHover&&this._valueGet()!=h().join("")&&L(this,h())}).bind("blur.inputmask",function(){var c=e(this),d=this._valueGet(),k=h();c.removeClass("focus.inputmask");E!=h().join("")&&c.change();g.clearMaskOnLostFocus&&""!=d&&(d==f().join("")?this._valueSet(""):Y(this));R(k)||(c.trigger("incomplete"),g.clearIncomplete&&(e.each(a,function(a,b){"object"==typeof b&&(b.buffer=b._buffer.slice(),b.lastValidPosition=-1)}),
b=0,g.clearMaskOnLostFocus?this._valueSet(""):(k=f().slice(),L(this,k))))}).bind("focus.inputmask",function(){var a=e(this),b=this._valueGet();g.showMaskOnFocus&&!a.hasClass("focus.inputmask")&&(!g.showMaskOnHover||g.showMaskOnHover&&""==b)&&this._valueGet()!=h().join("")&&L(this,h(),s(c().lastValidPosition));a.addClass("focus.inputmask");E=h().join("")}).bind("mouseleave.inputmask",function(){var a=e(this);g.clearMaskOnLostFocus&&(a.hasClass("focus.inputmask")||this._valueGet()==a.attr("placeholder")||
(this._valueGet()==f().join("")||""==this._valueGet()?this._valueSet(""):Y(this)))}).bind("click.inputmask",function(){var a=this;setTimeout(function(){var b=w(a),d=h();if(b.begin==b.end){var b=g.isRTL?O(b.begin):b.begin,f=c().lastValidPosition,d=g.isNumeric?!1===g.skipRadixDance&&""!=g.radixPoint&&-1!=e.inArray(g.radixPoint,d)?g.numericInput?s(e.inArray(g.radixPoint,d)):e.inArray(g.radixPoint,d):s(f):s(f);b<d?p(b)?w(a,b):w(a,s(b)):w(a,d)}},0)}).bind("dblclick.inputmask",function(){var a=this;setTimeout(function(){w(a,
0,s(c().lastValidPosition))},0)}).bind("keydown.inputmask",X).bind("keypress.inputmask",ea).bind("keyup.inputmask",Z).bind(F+".inputmask dragdrop.inputmask drop.inputmask",function(a){var b=this,c=e(b);if("propertychange"==a.type&&b._valueGet().length<=n())return!0;setTimeout(function(){M(b,!0,!1);R(h())&&c.trigger("complete");c.click()},0)}).bind("setvalue.inputmask",function(){M(this,!0);E=h().join("");this._valueGet()==f().join("")&&this._valueSet("")}).bind("_keypress.inputmask",ea).bind("complete.inputmask",
g.oncomplete).bind("incomplete.inputmask",g.onincomplete).bind("cleared.inputmask",g.oncleared);M(v,!0,!1);E=h().join("");var ca;try{ca=document.activeElement}catch(fa){}ca===v?(t.addClass("focus.inputmask"),w(v,s(c().lastValidPosition))):g.clearMaskOnLostFocus?h().join("")==f().join("")?v._valueSet(""):Y(v):L(v,h());P(v)}};return this}var g=e.extend(!0,{},e.inputmask.defaults,c),u=null!==navigator.userAgent.match(/msie 10/i),N=null!==navigator.userAgent.match(/iphone/i),v=null!==navigator.userAgent.match(/android.*safari.*/i),
F=d("paste")&&!u?"paste":d("input")?"input":"propertychange",l,m=0;v&&(u=navigator.userAgent.match(/safari.*/i),parseInt(RegExp(/[0-9]+/).exec(u)));if("string"===typeof a)switch(a){case "mask":return f(g.alias,c),l=k(),this.each(function(){q(e.extend(!0,{},l),0).mask(this)});case "unmaskedvalue":return u=e(this),u.data("_inputmask")?(l=u.data("_inputmask").masksets,m=u.data("_inputmask").activeMasksetIndex,g=u.data("_inputmask").opts,q(l,m).unmaskedvalue(u)):u.val();case "remove":return this.each(function(){var a=
e(this);if(a.data("_inputmask")){l=a.data("_inputmask").masksets;m=a.data("_inputmask").activeMasksetIndex;g=a.data("_inputmask").opts;this._valueSet(q(l,m).unmaskedvalue(a,!0));a.removeData("_inputmask");a.unbind(".inputmask");a.removeClass("focus.inputmask");var b;Object.getOwnPropertyDescriptor&&(b=Object.getOwnPropertyDescriptor(this,"value"));b&&b.get?this._valueGet&&Object.defineProperty(this,"value",{get:this._valueGet,set:this._valueSet}):document.__lookupGetter__&&this.__lookupGetter__("value")&&
this._valueGet&&(this.__defineGetter__("value",this._valueGet),this.__defineSetter__("value",this._valueSet));try{delete this._valueGet,delete this._valueSet}catch(c){this._valueSet=this._valueGet=void 0}}});case "getemptymask":return this.data("_inputmask")?(l=this.data("_inputmask").masksets,m=this.data("_inputmask").activeMasksetIndex,l[m]._buffer.join("")):"";case "hasMaskedValue":return this.data("_inputmask")?!this.data("_inputmask").opts.autoUnmask:!1;case "isComplete":return l=this.data("_inputmask").masksets,
m=this.data("_inputmask").activeMasksetIndex,g=this.data("_inputmask").opts,q(l,m).isComplete(this[0]._valueGet().split(""));case "getmetadata":if(this.data("_inputmask"))return l=this.data("_inputmask").masksets,m=this.data("_inputmask").activeMasksetIndex,l[m].metadata;return;default:return f(a,c)||(g.mask=a),l=k(),this.each(function(){q(e.extend(!0,{},l),m).mask(this)})}else{if("object"==typeof a)return g=e.extend(!0,{},e.inputmask.defaults,a),f(g.alias,a),l=k(),this.each(function(){q(e.extend(!0,
{},l),m).mask(this)});if(void 0==a)return this.each(function(){var a=e(this).attr("data-inputmask");if(a&&""!=a)try{var a=a.replace(RegExp("'","g"),'"'),b=e.parseJSON("{"+a+"}");e.extend(!0,b,c);g=e.extend(!0,{},e.inputmask.defaults,b);f(g.alias,b);g.alias=void 0;e(this).inputmask(g)}catch(d){}})}return this})})(jQuery);
(function(e){e.extend(e.inputmask.defaults.definitions,{A:{validator:"[A-Za-z]",cardinality:1,casing:"upper"},"#":{validator:"[A-Za-z\u0410-\u044f\u0401\u04510-9]",cardinality:1,casing:"upper"}});e.extend(e.inputmask.defaults.aliases,{url:{mask:"ir",placeholder:"",separator:"",defaultPrefix:"http://",regex:{urlpre1:/[fh]/,urlpre2:/(ft|ht)/,urlpre3:/(ftp|htt)/,urlpre4:/(ftp:|http|ftps)/,urlpre5:/(ftp:\/|ftps:|http:|https)/,urlpre6:/(ftp:\/\/|ftps:\/|http:\/|https:)/,urlpre7:/(ftp:\/\/|ftps:\/\/|http:\/\/|https:\/)/,
urlpre8:/(ftp:\/\/|ftps:\/\/|http:\/\/|https:\/\/)/},definitions:{i:{validator:function(a,c,d,f,b){return!0},cardinality:8,prevalidator:function(){for(var a=[],c=0;8>c;c++)a[c]=function(){var a=c;return{validator:function(c,b,e,k,A){if(A.regex["urlpre"+(a+1)]){var q=c;0<a+1-c.length&&(q=b.join("").substring(0,a+1-c.length)+""+q);c=A.regex["urlpre"+(a+1)].test(q);if(!k&&!c){e-=a;for(k=0;k<A.defaultPrefix.length;k++)b[e]=A.defaultPrefix[k],e++;for(k=0;k<q.length-1;k++)b[e]=q[k],e++;return{pos:e}}return c}return!1},
cardinality:a}}();return a}()},r:{validator:".",cardinality:50}},insertMode:!1,autoUnmask:!1},ip:{mask:["[[x]y]z.[[x]y]z.[[x]y]z.x[yz]","[[x]y]z.[[x]y]z.[[x]y]z.[[x]y][z]"],definitions:{x:{validator:"[012]",cardinality:1,definitionSymbol:"i"},y:{validator:function(a,c,d,f,b){a=-1<d-1&&"."!=c[d-1]?c[d-1]+a:"0"+a;return/2[0-5]|[01][0-9]/.test(a)},cardinality:1,definitionSymbol:"i"},z:{validator:function(a,c,d,f,b){-1<d-1&&"."!=c[d-1]?(a=c[d-1]+a,a=-1<d-2&&"."!=c[d-2]?c[d-2]+a:"0"+a):a="00"+a;return/25[0-5]|2[0-4][0-9]|[01][0-9][0-9]/.test(a)},
cardinality:1,definitionSymbol:"i"}}}})})(jQuery);
(function(e){e.extend(e.inputmask.defaults.definitions,{h:{validator:"[01][0-9]|2[0-3]",cardinality:2,prevalidator:[{validator:"[0-2]",cardinality:1}]},s:{validator:"[0-5][0-9]",cardinality:2,prevalidator:[{validator:"[0-5]",cardinality:1}]},d:{validator:"0[1-9]|[12][0-9]|3[01]",cardinality:2,prevalidator:[{validator:"[0-3]",cardinality:1}]},m:{validator:"0[1-9]|1[012]",cardinality:2,prevalidator:[{validator:"[01]",cardinality:1}]},y:{validator:"(19|20)\\d{2}",cardinality:4,prevalidator:[{validator:"[12]",
cardinality:1},{validator:"(19|20)",cardinality:2},{validator:"(19|20)\\d",cardinality:3}]}});e.extend(e.inputmask.defaults.aliases,{"dd/mm/yyyy":{mask:"1/2/y",placeholder:"dd/mm/yyyy",regex:{val1pre:/[0-3]/,val1:/0[1-9]|[12][0-9]|3[01]/,val2pre:function(a){a=e.inputmask.escapeRegex.call(this,a);return RegExp("((0[1-9]|[12][0-9]|3[01])"+a+"[01])")},val2:function(a){a=e.inputmask.escapeRegex.call(this,a);return RegExp("((0[1-9]|[12][0-9])"+a+"(0[1-9]|1[012]))|(30"+a+"(0[13-9]|1[012]))|(31"+a+"(0[13578]|1[02]))")}},
leapday:"29/02/",separator:"/",yearrange:{minyear:1900,maxyear:2099},isInYearRange:function(a,c,d){var f=parseInt(a.concat(c.toString().slice(a.length)));a=parseInt(a.concat(d.toString().slice(a.length)));return(NaN!=f?c<=f&&f<=d:!1)||(NaN!=a?c<=a&&a<=d:!1)},determinebaseyear:function(a,c,d){var f=(new Date).getFullYear();if(a>f)return a;if(c<f){for(var f=c.toString().slice(0,2),b=c.toString().slice(2,4);c<f+d;)f--;c=f+b;return a>c?a:c}return f},onKeyUp:function(a,c,d){c=e(this);a.ctrlKey&&a.keyCode==
d.keyCode.RIGHT&&(a=new Date,c.val(a.getDate().toString()+(a.getMonth()+1).toString()+a.getFullYear().toString()))},definitions:{1:{validator:function(a,c,d,f,b){var e=b.regex.val1.test(a);return f||e||a.charAt(1)!=b.separator&&-1=="-./".indexOf(a.charAt(1))||!(e=b.regex.val1.test("0"+a.charAt(0)))?e:(c[d-1]="0",{pos:d,c:a.charAt(0)})},cardinality:2,prevalidator:[{validator:function(a,c,d,f,b){var e=b.regex.val1pre.test(a);return f||e||!(e=b.regex.val1.test("0"+a))?e:(c[d]="0",d++,{pos:d})},cardinality:1}]},
2:{validator:function(a,c,d,f,b){var e=c.join("").substr(0,3),k=b.regex.val2(b.separator).test(e+a);return f||k||a.charAt(1)!=b.separator&&-1=="-./".indexOf(a.charAt(1))||!(k=b.regex.val2(b.separator).test(e+"0"+a.charAt(0)))?k:(c[d-1]="0",{pos:d,c:a.charAt(0)})},cardinality:2,prevalidator:[{validator:function(a,c,d,f,b){var e=c.join("").substr(0,3),k=b.regex.val2pre(b.separator).test(e+a);return f||k||!(k=b.regex.val2(b.separator).test(e+"0"+a))?k:(c[d]="0",d++,{pos:d})},cardinality:1}]},y:{validator:function(a,
c,d,f,b){if(b.isInYearRange(a,b.yearrange.minyear,b.yearrange.maxyear)){if(c.join("").substr(0,6)!=b.leapday)return!0;a=parseInt(a,10);return 0===a%4?0===a%100?0===a%400?!0:!1:!0:!1}return!1},cardinality:4,prevalidator:[{validator:function(a,c,d,f,b){var e=b.isInYearRange(a,b.yearrange.minyear,b.yearrange.maxyear);if(!f&&!e){f=b.determinebaseyear(b.yearrange.minyear,b.yearrange.maxyear,a+"0").toString().slice(0,1);if(e=b.isInYearRange(f+a,b.yearrange.minyear,b.yearrange.maxyear))return c[d++]=f[0],
{pos:d};f=b.determinebaseyear(b.yearrange.minyear,b.yearrange.maxyear,a+"0").toString().slice(0,2);if(e=b.isInYearRange(f+a,b.yearrange.minyear,b.yearrange.maxyear))return c[d++]=f[0],c[d++]=f[1],{pos:d}}return e},cardinality:1},{validator:function(a,c,d,f,b){var e=b.isInYearRange(a,b.yearrange.minyear,b.yearrange.maxyear);if(!f&&!e){f=b.determinebaseyear(b.yearrange.minyear,b.yearrange.maxyear,a).toString().slice(0,2);if(e=b.isInYearRange(a[0]+f[1]+a[1],b.yearrange.minyear,b.yearrange.maxyear))return c[d++]=
f[1],{pos:d};f=b.determinebaseyear(b.yearrange.minyear,b.yearrange.maxyear,a).toString().slice(0,2);b.isInYearRange(f+a,b.yearrange.minyear,b.yearrange.maxyear)?c.join("").substr(0,6)!=b.leapday?e=!0:(b=parseInt(a,10),e=0===b%4?0===b%100?0===b%400?!0:!1:!0:!1):e=!1;if(e)return c[d-1]=f[0],c[d++]=f[1],c[d++]=a[0],{pos:d}}return e},cardinality:2},{validator:function(a,c,d,f,b){return b.isInYearRange(a,b.yearrange.minyear,b.yearrange.maxyear)},cardinality:3}]}},insertMode:!1,autoUnmask:!1},"mm/dd/yyyy":{placeholder:"mm/dd/yyyy",
alias:"dd/mm/yyyy",regex:{val2pre:function(a){a=e.inputmask.escapeRegex.call(this,a);return RegExp("((0[13-9]|1[012])"+a+"[0-3])|(02"+a+"[0-2])")},val2:function(a){a=e.inputmask.escapeRegex.call(this,a);return RegExp("((0[1-9]|1[012])"+a+"(0[1-9]|[12][0-9]))|((0[13-9]|1[012])"+a+"30)|((0[13578]|1[02])"+a+"31)")},val1pre:/[01]/,val1:/0[1-9]|1[012]/},leapday:"02/29/",onKeyUp:function(a,c,d){c=e(this);a.ctrlKey&&a.keyCode==d.keyCode.RIGHT&&(a=new Date,c.val((a.getMonth()+1).toString()+a.getDate().toString()+
a.getFullYear().toString()))}},"yyyy/mm/dd":{mask:"y/1/2",placeholder:"yyyy/mm/dd",alias:"mm/dd/yyyy",leapday:"/02/29",onKeyUp:function(a,c,d){c=e(this);a.ctrlKey&&a.keyCode==d.keyCode.RIGHT&&(a=new Date,c.val(a.getFullYear().toString()+(a.getMonth()+1).toString()+a.getDate().toString()))},definitions:{2:{validator:function(a,c,d,f,b){var e=c.join("").substr(5,3),k=b.regex.val2(b.separator).test(e+a);if(!(f||k||a.charAt(1)!=b.separator&&-1=="-./".indexOf(a.charAt(1)))&&(k=b.regex.val2(b.separator).test(e+
"0"+a.charAt(0))))return c[d-1]="0",{pos:d,c:a.charAt(0)};if(k){if(c.join("").substr(4,4)+a!=b.leapday)return!0;a=parseInt(c.join("").substr(0,4),10);return 0===a%4?0===a%100?0===a%400?!0:!1:!0:!1}return k},cardinality:2,prevalidator:[{validator:function(a,c,d,f,b){var e=c.join("").substr(5,3),k=b.regex.val2pre(b.separator).test(e+a);return f||k||!(k=b.regex.val2(b.separator).test(e+"0"+a))?k:(c[d]="0",d++,{pos:d})},cardinality:1}]}}},"dd.mm.yyyy":{mask:"1.2.y",placeholder:"dd.mm.yyyy",leapday:"29.02.",
separator:".",alias:"dd/mm/yyyy"},"dd-mm-yyyy":{mask:"1-2-y",placeholder:"dd-mm-yyyy",leapday:"29-02-",separator:"-",alias:"dd/mm/yyyy"},"mm.dd.yyyy":{mask:"1.2.y",placeholder:"mm.dd.yyyy",leapday:"02.29.",separator:".",alias:"mm/dd/yyyy"},"mm-dd-yyyy":{mask:"1-2-y",placeholder:"mm-dd-yyyy",leapday:"02-29-",separator:"-",alias:"mm/dd/yyyy"},"yyyy.mm.dd":{mask:"y.1.2",placeholder:"yyyy.mm.dd",leapday:".02.29",separator:".",alias:"yyyy/mm/dd"},"yyyy-mm-dd":{mask:"y-1-2",placeholder:"yyyy-mm-dd",leapday:"-02-29",
separator:"-",alias:"yyyy/mm/dd"},datetime:{mask:"1/2/y h:s",placeholder:"dd/mm/yyyy hh:mm",alias:"dd/mm/yyyy",regex:{hrspre:/[012]/,hrs24:/2[0-9]|1[3-9]/,hrs:/[01][0-9]|2[0-3]/,ampm:/^[a|p|A|P][m|M]/},timeseparator:":",hourFormat:"24",definitions:{h:{validator:function(a,c,d,f,b){var e=b.regex.hrs.test(a);return f||e||a.charAt(1)!=b.timeseparator&&-1=="-.:".indexOf(a.charAt(1))||!(e=b.regex.hrs.test("0"+a.charAt(0)))?e&&"24"!==b.hourFormat&&b.regex.hrs24.test(a)?(a=parseInt(a,10),c[d+5]=24==a?"a":
"p",c[d+6]="m",a-=12,10>a?(c[d]=a.toString(),c[d-1]="0"):(c[d]=a.toString().charAt(1),c[d-1]=a.toString().charAt(0)),{pos:d,c:c[d]}):e:(c[d-1]="0",c[d]=a.charAt(0),d++,{pos:d})},cardinality:2,prevalidator:[{validator:function(a,c,d,f,b){var e=b.regex.hrspre.test(a);return f||e||!(e=b.regex.hrs.test("0"+a))?e:(c[d]="0",d++,{pos:d})},cardinality:1}]},t:{validator:function(a,c,d,e,b){return b.regex.ampm.test(a+"m")},casing:"lower",cardinality:1}},insertMode:!1,autoUnmask:!1},datetime12:{mask:"1/2/y h:s t\\m",
placeholder:"dd/mm/yyyy hh:mm xm",alias:"datetime",hourFormat:"12"},"hh:mm t":{mask:"h:s t\\m",placeholder:"hh:mm xm",alias:"datetime",hourFormat:"12"},"h:s t":{mask:"h:s t\\m",placeholder:"hh:mm xm",alias:"datetime",hourFormat:"12"},"hh:mm:ss":{mask:"h:s:s",autoUnmask:!1},"hh:mm":{mask:"h:s",autoUnmask:!1},date:{alias:"dd/mm/yyyy"},"mm/yyyy":{mask:"1/y",placeholder:"mm/yyyy",leapday:"donotuse",separator:"/",alias:"mm/dd/yyyy"}})})(jQuery);
(function(e){e.extend(e.inputmask.defaults.aliases,{decimal:{mask:"~",placeholder:"",repeat:"*",greedy:!1,numericInput:!1,isNumeric:!0,digits:"*",groupSeparator:"",radixPoint:".",groupSize:3,autoGroup:!1,allowPlus:!0,allowMinus:!0,integerDigits:"*",defaultValue:"",prefix:"",suffix:"",getMaskLength:function(a,c,d,f,b){var h=a.length;c||("*"==d?h=f.length+1:1<d&&(h+=a.length*(d-1)));a=e.inputmask.escapeRegex.call(this,b.groupSeparator);b=e.inputmask.escapeRegex.call(this,b.radixPoint);f=f.join("");
b=f.replace(RegExp(a,"g"),"").replace(RegExp(b),"");return h+(f.length-b.length)},postFormat:function(a,c,d,f){if(""==f.groupSeparator)return c;var b=a.slice();e.inArray(f.radixPoint,a);d||b.splice(c,0,"?");b=b.join("");if(f.autoGroup||d&&-1!=b.indexOf(f.groupSeparator)){for(var h=e.inputmask.escapeRegex.call(this,f.groupSeparator),b=b.replace(RegExp(h,"g"),""),h=b.split(f.radixPoint),b=h[0],k=RegExp("([-+]?[\\d?]+)([\\d?]{"+f.groupSize+"})");k.test(b);)b=b.replace(k,"$1"+f.groupSeparator+"$2"),b=
b.replace(f.groupSeparator+f.groupSeparator,f.groupSeparator);1<h.length&&(b+=f.radixPoint+h[1])}a.length=b.length;f=0;for(h=b.length;f<h;f++)a[f]=b.charAt(f);b=e.inArray("?",a);d||a.splice(b,1);return d?c:b},regex:{number:function(a){var c=e.inputmask.escapeRegex.call(this,a.groupSeparator),d=e.inputmask.escapeRegex.call(this,a.radixPoint),f=isNaN(a.digits)?a.digits:"{0,"+a.digits+"}";return RegExp("^"+("["+(a.allowPlus?"+":"")+(a.allowMinus?"-":"")+"]?")+"(\\d+|\\d{1,"+a.groupSize+"}(("+c+"\\d{"+
a.groupSize+"})?)+)("+d+"\\d"+f+")?$")}},onKeyDown:function(a,c,d){var f=e(this);if(a.keyCode==d.keyCode.TAB){if(a=e.inArray(d.radixPoint,c),-1!=a){for(var b=f.data("_inputmask").masksets,f=f.data("_inputmask").activeMasksetIndex,h=1;h<=d.digits&&h<d.getMaskLength(b[f]._buffer,b[f].greedy,b[f].repeat,c,d);h++)if(void 0==c[a+h]||""==c[a+h])c[a+h]="0";this._valueSet(c.join(""))}}else if(a.keyCode==d.keyCode.DELETE||a.keyCode==d.keyCode.BACKSPACE)return d.postFormat(c,0,!0,d),this._valueSet(c.join("")),
!0},definitions:{"~":{validator:function(a,c,d,f,b){if(""==a)return!1;if(!f&&1>=d&&"0"===c[0]&&/[\d-]/.test(a)&&1==c.length)return c[0]="",{pos:0};var h=f?c.slice(0,d):c.slice();h.splice(d,0,a);var h=h.join(""),k=e.inputmask.escapeRegex.call(this,b.groupSeparator),h=h.replace(RegExp(k,"g"),""),k=b.regex.number(b).test(h);if(!k&&(h+="0",k=b.regex.number(b).test(h),!k)){k=h.lastIndexOf(b.groupSeparator);for(i=h.length-k;3>=i;i++)h+="0";k=b.regex.number(b).test(h);if(!k&&!f&&a==b.radixPoint&&(k=b.regex.number(b).test("0"+
h+"0")))return c[d]="0",d++,{pos:d}}return!1==k||f||a==b.radixPoint?k:{pos:b.postFormat(c,d,!1,b)}},cardinality:1,prevalidator:null}},insertMode:!0,autoUnmask:!1},integer:{regex:{number:function(a){var c=e.inputmask.escapeRegex.call(this,a.groupSeparator);return RegExp("^"+(a.allowPlus||a.allowMinus?"["+(a.allowPlus?"+":"")+(a.allowMinus?"-":"")+"]?":"")+"(\\d+|\\d{1,"+a.groupSize+"}(("+c+"\\d{"+a.groupSize+"})?)+)$")}},alias:"decimal"}})})(jQuery);
(function(e){e.extend(e.inputmask.defaults.aliases,{Regex:{mask:"r",greedy:!1,repeat:"*",regex:null,regexTokens:null,tokenizer:/\[\^?]?(?:[^\\\]]+|\\[\S\s]?)*]?|\\(?:0(?:[0-3][0-7]{0,2}|[4-7][0-7]?)?|[1-9][0-9]*|x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4}|c[A-Za-z]|[\S\s]?)|\((?:\?[:=!]?)?|(?:[?*+]|\{[0-9]+(?:,[0-9]*)?\})\??|[^.?*+^${[()|\\]+|./g,quantifierFilter:/[0-9]+[^,]/,definitions:{r:{validator:function(a,c,d,e,b){function h(){this.matches=[];this.isLiteral=this.isQuantifier=this.isGroup=!1}function k(){var a=
new h,c,d=[];for(b.regexTokens=[];c=b.tokenizer.exec(b.regex);)switch(c=c[0],c.charAt(0)){case "[":case "\\":0<d.length?d[d.length-1].matches.push(c):a.matches.push(c);break;case "(":!a.isGroup&&0<a.matches.length&&b.regexTokens.push(a);a=new h;a.isGroup=!0;d.push(a);break;case ")":c=d.pop();0<d.length?d[d.length-1].matches.push(c):(b.regexTokens.push(c),a=new h);break;case "{":var e=new h;e.isQuantifier=!0;e.matches.push(c);0<d.length?d[d.length-1].matches.push(e):a.matches.push(e);break;default:e=
new h,e.isLiteral=!0,e.matches.push(c),0<d.length?d[d.length-1].matches.push(e):a.matches.push(e)}0<a.matches.length&&b.regexTokens.push(a)}function A(a,c){var d=!1;c&&(q+="(",g++);for(var e=0;e<a.matches.length;e++){var f=a.matches[e];if(!0==f.isGroup)d=A(f,!0);else if(!0==f.isQuantifier){for(var f=f.matches[0],h=b.quantifierFilter.exec(f)[0].replace("}",""),h=q+"{1,"+h+"}",k=0;k<g;k++)h+=")";d=RegExp("^("+h+")$");d=d.test(u);q+=f}else if(!0==f.isLiteral){for(var f=f.matches[0],h=q,S="",k=0;k<g;k++)S+=
")";for(k=0;k<f.length&&!(h=(h+f[k]).replace(/\|$/,""),d=RegExp("^("+h+S+")$"),d=d.test(u));k++);q+=f}else{q+=f;h=q.replace(/\|$/,"");for(k=0;k<g;k++)h+=")";d=RegExp("^("+h+")$");d=d.test(u)}if(d)break}c&&(q+=")",g--);return d}null==b.regexTokens&&k();e=c.slice();var q="";c=!1;var g=0;e.splice(d,0,a);var u=e.join("");for(a=0;a<b.regexTokens.length&&!(h=b.regexTokens[a],c=A(h,h.isGroup));a++);return c},cardinality:1}}}})})(jQuery);
(function(e){e.extend(e.inputmask.defaults.aliases,{phone:{url:"phone-codes/phone-codes.json",mask:function(a){a.definitions={p:{validator:function(){return!1},cardinality:1},"#":{validator:"[0-9]",cardinality:1}};var c=[];e.ajax({url:a.url,async:!1,dataType:"json",success:function(a){c=a}});c.splice(0,0,"+p(ppp)ppp-pppp");return c}}})})(jQuery);
