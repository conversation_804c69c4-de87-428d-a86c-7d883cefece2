
/*
 * aciTree jQuery Plugin v4.5.0-rc.3
 * http://acoderinsights.ro
 *
 * Copyright (c) 2014 Dragos Ursu
 * Dual licensed under the MIT or GPL Version 2 licenses.
 *
 * Require jQuery Library >= v1.9.0 http://jquery.com
 * + aciPlugin >= v1.5.1 https://github.com/dragosu/jquery-aciPlugin
 */

// DOM
aciPluginClass.plugins.aciTree_dom={container:function(b){var a=b.lastChild;if(a&&(a.nodeName=="UL")){return a}return null},firstChild:function(b,d){var a=this.container(b);if(a){var c=a.firstChild;if(d){while(c&&!d.call(this,c)){c=c.nextSibling}}return c}return null},lastChild:function(c,d){var a=this.container(c);if(a){var b=a.lastChild;if(d){while(b&&!d.call(this,b)){b=b.previousSibling}}return b}return null},prev:function(b,c){var a=b.previousSibling;if(c){while(a&&!c.call(this,a)){a=a.previousSibling}}return a},next:function(b,c){var a=b.nextSibling;if(c){while(a&&!c.call(this,a)){a=a.nextSibling}}return a},prevAll:function(f,h){var e,d,g,a,c,b;while(true){e=this.prev(f);if(e){if(h){a=h.call(this,e);if(a===null){f=e;continue}}d=this.lastChild(e);if(d){if(h&&(h.call(this,d)===null)){f=d;continue}c=false;while(g=this.lastChild(d)){d=g;if(h){a=h.call(this,d);if(a===null){f=d;c=true;break}}}if(c){continue}if(h){a=h.call(this,d);if(a){return d}else{if(a!==null){f=d;continue}}}else{return d}}else{if(!h||a){return e}else{f=e;continue}}}b=this.parent(f);if(b){if(h){a=h.call(this,b);if(a){return b}else{f=b}}else{return b}}else{return null}}return null},nextAll:function(d,g){var e,a,c,b,f;while(true){e=this.firstChild(d);if(e){if(g){a=g.call(this,e);if(a){return e}else{d=e;if(a!==null){continue}}}else{return e}}while(true){c=this.next(d);if(c){if(g){a=g.call(this,c);if(a){return c}else{d=c;if(a!==null){break}else{continue}}}else{return c}}else{b=d;f=null;while(b=this.parent(b)){c=this.next(b);if(c){if(g){a=g.call(this,c);if(a){return c}else{d=c;if(a!==null){f=true}else{f=false}break}}else{return c}}}if(f!==null){if(f){break}else{continue}}return null}}}return null},first:function(b,d){var a=this.container(b);if(a){var c=a.firstChild;if(c){if(d&&!d.call(this,c)){return this.nextAll(c,d)}return c}}return null},last:function(c,e){var a=this.container(c);if(a){var b=a.lastChild;if(b){if(e&&(e.call(this,b)===null)){return this.prevAll(b,e)}else{var d;while(d=this.lastChild(b)){b=d}if(e&&!e.call(this,b)){return this.prevAll(b,e)}return b}}}return null},children:function(b,f,h){var a=[],i=[],d,c,g;var e=this.firstChild(b);if(e){while(true){g=false;do{if(h){d=h.call(this,e);if(d){a.push(e)}if(f&&(d!==null)){c=this.firstChild(e);if(c){i.push(e);e=c;g=true;break}}}else{a.push(e);if(f){c=this.firstChild(e);if(c){i.push(e);e=c;g=true;break}}}}while(e=e.nextSibling);if(!g){while(e=i.pop()){e=e.nextSibling;if(e){break}}if(!e){break}}}}return a},childrenTill:function(d,g){var e=[],a,b,c;var f=d.firstChild;if(f){while(true){c=false;do{a=g.call(this,f);if(a){return f}else{if(a===null){return null}}b=f.firstChild;if(b){e.push(f);f=b;c=true;break}}while(f=f.nextSibling);if(!c){while(f=e.pop()){f=f.nextSibling;if(f){break}}if(!f){break}}}}return null},childrenByClass:function(b,a){if(b.getElementsByClassName){var c=b.getElementsByClassName(a instanceof Array?a.join(" "):a);return c?c[0]:null}else{return this.childrenTill(b,function(d){return this.hasClass(d,a)})}},parent:function(b){var a=b.parentNode.parentNode;if(a&&(a.nodeName=="LI")){return a}return null},parentFrom:function(a){while(a.nodeName!="LI"){a=a.parentNode;if(!a){return null}}return a},parentTill:function(b,c){var a;while(b=b.parentNode){a=c.call(this,b);if(a){return b}else{if(a===null){return null}}}return null},parentByClass:function(b,a){return this.parentTill(b,function(c){return this.hasClass(c,a)})},hasClass:function(e,d,a){var b=" "+e.className+" ";if(a instanceof Array){for(var c=0;c<a.length;c++){if(b.indexOf(" "+a[c]+" ")!=-1){return false}}}else{if(a&&b.indexOf(" "+a+" ")!=-1){return false}}if(d instanceof Array){for(var c=0;c<d.length;c++){if(b.indexOf(" "+d[c]+" ")==-1){return false}}}else{if(b.indexOf(" "+d+" ")==-1){return false}}return true},withClass:function(b,e,a){var d=[];for(var c=0;c<b.length;c++){if(this.hasClass(b[c],e,a)){d.push(b[c])}}return d},hasAnyClass:function(e,d,a){var b=" "+e.className+" ";if(a instanceof Array){for(var c=0;c<a.length;c++){if(b.indexOf(" "+a[c]+" ")!=-1){return false}}}else{if(a&&b.indexOf(" "+a+" ")!=-1){return false}}if(d instanceof Array){for(var c=0;c<d.length;c++){if(b.indexOf(" "+d[c]+" ")!=-1){return true}}}else{if(b.indexOf(" "+d+" ")!=-1){return true}}return false},withAnyClass:function(b,e,a){var d=[];for(var c=0;c<b.length;c++){if(this.hasAnyClass(b[c],e,a)){d.push(b[c])}}return d},addClass:function(e,d){var b=" "+e.className+" ",a="";if(d instanceof Array){for(var c=0;c<d.length;c++){if(b.indexOf(" "+d[c]+" ")==-1){a+=" "+d[c]}}}else{if(b.indexOf(" "+d+" ")==-1){a+=" "+d}}if(a){e.className=e.className+a;return true}return false},addListClass:function(a,c,d){for(var b=0;b<a.length;b++){this.addClass(a[b],c);if(d){d.call(this,a[b])}}},removeClass:function(d,c){var a=" "+d.className+" ";if(c instanceof Array){for(var b=0;b<c.length;b++){a=a.replace(" "+c[b]+" "," ")}}else{a=a.replace(" "+c+" "," ")}a=a.substr(1,a.length-2);if(d.className!=a){d.className=a;return true}return false},removeListClass:function(a,c,d){for(var b=0;b<a.length;b++){this.removeClass(a[b],c);if(d){d.call(this,a[b])}}},toggleClass:function(b,a,c){if(c){return this.addClass(b,a)}else{return this.removeClass(b,a)}},toggleListClass:function(a,c,d,e){for(var b=0;b<a.length;b++){this.toggleClass(a[b],c,d);if(e){e.call(this,a[b])}}},addRemoveClass:function(d,e,f){var b=" "+d.className+" ";if(f){if(f instanceof Array){for(var c=0;c<f.length;c++){b=b.replace(" "+f[c]+" "," ")}}else{b=b.replace(" "+f+" "," ")}}if(e){var a="";if(e instanceof Array){for(var c=0;c<e.length;c++){if(b.indexOf(" "+e[c]+" ")==-1){a+=e[c]+" "}}}else{if(b.indexOf(" "+e+" ")==-1){a+=e+" "}}b+=a}b=b.substr(1,b.length-2);if(d.className!=b){d.className=b;return true}return false},addRemoveListClass:function(a,c,d,e){for(var b=0;b<a.length;b++){this.addRemoveClass(a[b],c,d);if(e){e.call(this,a[b])}}}};

// core
(function(d,c,f){var b={ajax:{url:null,dataType:"json"},dataSource:null,rootData:null,queue:{async:4,interval:50,delay:20},loaderDelay:500,expand:false,collapse:false,unique:false,empty:false,show:{props:{height:"show"},duration:"medium",easing:"linear"},animateRoot:true,hide:{props:{height:"hide"},duration:"medium",easing:"linear"},view:{duration:"medium",easing:"linear"},ajaxHook:function(h,g){g.url+=(h?this.getId(h):"")},itemHook:function(h,i,g,j){},serialize:function(g,i,h){if(typeof i=="object"){return h}else{return"|"+h}}};var e={__extend:function(){d.extend(this._instance,{queue:new this._queue(this,this._instance.options.queue)});d.extend(this._private,{locked:false,itemClone:{},loaderHide:null,loaderInterval:null,delayBusy:0})},init:function(h){h=this._options(h);if(this.wasInit()){this._trigger(null,"wasinit",h);this._fail(null,h);return}if(this.isLocked()){this._trigger(null,"locked",h);this._fail(null,h);return}if(!this._trigger(null,"beforeinit",h)){this._trigger(null,"initfail",h);this._fail(null,h);return}this._private.locked=true;this._instance.jQuery.addClass("aciTree"+this._instance.index).attr("role","tree").on("click"+this._instance.nameSpace,".aciTreeButton",this.proxy(function(k){var j=this.itemFrom(k.target);if(!this.isBusy(j)){this.toggle(j,{collapse:this._instance.options.collapse,expand:this._instance.options.expand,unique:this._instance.options.unique})}})).on("mouseenter"+this._instance.nameSpace+" mouseleave"+this._instance.nameSpace,".aciTreePush",function(k){var j=k.target;if(!a.hasClass(j,"aciTreePush")){j=a.parentByClass(j,"aciTreePush")}a.toggleClass(j,"aciTreeHover",k.type=="mouseenter")}).on("mouseenter"+this._instance.nameSpace+" mouseleave"+this._instance.nameSpace,".aciTreeLine",function(k){var j=k.target;if(!a.hasClass(j,"aciTreeLine")){j=a.parentByClass(j,"aciTreeLine")}a.toggleClass(j,"aciTreeHover",k.type=="mouseenter")});this._initHook();var i=this.proxy(function(){this._super();this._private.locked=false;this._trigger(null,"init",h);this._success(null,h)});var g=this.proxy(function(){this._super();this._private.locked=false;this._trigger(null,"initfail",h);this._fail(null,h)});if(this._instance.options.rootData){this.loadFrom(null,this._inner(h,{success:i,fail:g,itemData:this._instance.options.rootData}))}else{if(this._instance.options.ajax.url){this.ajaxLoad(null,this._inner(h,{success:i,fail:g}))}else{i.apply(this)}}},_initHook:function(){},isLocked:function(){return this._private.locked},_format:function(p,h){if(!(h instanceof Array)){return p}var j=p.split(/(%[0-9]+)/gm);var q="",g,l,o=false,m;var n=new c.RegExp("^%[0-9]+$");for(var k=0;k<j.length;k++){g=j[k];m=g.length;if(m){if(!o&&n.test(g)){l=c.parseInt(g.substr(1))-1;if((l>=0)&&(l<h.length)){q+=h[l];continue}}else{o=false;if(g.substr(m-1)=="%"){if(g.substr(m-2)!="%%"){o=true}g=g.substr(0,m-1)}}q+=g}}return q},_coreDOM:{leaf:function(g){a.addRemoveListClass(g.toArray(),"aciTreeLeaf",["aciTreeInode","aciTreeInodeMaybe","aciTreeOpen"],function(h){h.removeAttribute("aria-expanded")})},inode:function(g,h){a.addRemoveListClass(g.toArray(),h?"aciTreeInode":"aciTreeInodeMaybe","aciTreeLeaf",function(i){i.setAttribute("aria-expanded",false)})},toggle:function(g,h){a.toggleListClass(g.toArray(),"aciTreeOpen",h,function(i){i.setAttribute("aria-expanded",h)})},oddEven:function(g,k){var j=g.toArray();for(var h=0;h<j.length;h++){a.addRemoveClass(j[h],k?"aciTreeOdd":"aciTreeEven",k?"aciTreeEven":"aciTreeOdd");k=!k}}},_queue:function(i,s){var l=false;var r=[],q=[];var p=0,h=0,m=0,o=0;var k=function(){if(l){o--;return}var t=new c.Date().getTime();if(m>t){o--;return}var v,u=false;if(p<s.async*2){v=r.shift()}if(!v&&(h<s.async)){v=q.shift();u=true}if(v){if(u){h++;v.call(i,function(){h--});if(o<40){o++;k()}}else{p++;v.call(i,function(){if(t-m>s.interval){m=t+s.delay}p--;if(o<40){o++;k()}})}}o--};var j=[];var g=function(){for(var t=0;t<4;t++){j[t]=c.setInterval(function(){if(o<20){o++;k()}},10)}};var n=function(){for(var t=0;t<j.length;t++){c.clearInterval(j[t])}};g();this.init=function(){this.destroy();g();return this};this.push=function(u,t){if(!l){if(t){q.push(u)}else{r.push(u)}}return this};this.busy=function(){return(p!=0)||(h!=0)||(r.length!=0)||(q.length!=0)};this.destroy=function(){l=true;n();r=[];q=[];p=0;h=0;m=0;o=0;l=false;return this}},_task:function(g,j){var h=0,i=false;this.push=function(l,k){h++;g.push(function(m){var n=this;l.call(this,function(){h--;if((h<1)&&!i){i=true;j.call(n,m)}else{m()}})},k)}},_options:function(h,j,n,i,m){var o=d.extend({uid:"ui",success:null,fail:null,notify:null,expand:this._instance.options.expand,collapse:this._instance.options.collapse,unique:this._instance.options.unique,unanimated:false,itemData:{}},h);var l=j?((typeof j=="string")?function(){this._trigger(m,j,o)}:j):null;var g=n?((typeof n=="string")?function(){this._trigger(m,n,o)}:n):null;var k=i?((typeof i=="string")?function(){this._trigger(m,i,o)}:i):null;if(l){if(h&&h.success){o.success=function(){l.apply(this,arguments);h.success.apply(this,arguments)}}else{o.success=l}}if(g){if(h&&h.fail){o.fail=function(){g.apply(this,arguments);h.fail.apply(this,arguments)}}else{o.fail=g}}if(k){if(h&&h.notify){o.notify=function(){k.apply(this,arguments);h.notify.apply(this,arguments)}}else{if(!o.notify&&h&&h.success){o.notify=function(){k.apply(this,arguments);h.success.apply(this,arguments)}}else{o.notify=k}}}else{if(!o.notify&&h&&h.success){o.notify=h.success}}return o},_inner:function(g,h){return d.extend({},g,{success:null,fail:null,notify:null},h)},_trigger:function(j,g,h){var i=d.Event("acitree");if(!h){h=this._options()}this._instance.jQuery.trigger(i,[this,j,g,h]);return !i.isDefaultPrevented()},_success:function(h,g){if(g&&g.success){g.success.call(this,h,g)}},_fail:function(h,g){if(g&&g.fail){g.fail.call(this,h,g)}},_notify:function(h,g){if(g&&g.notify){g.notify.call(this,h,g)}},_delayBusy:function(g,h){if((this._private.delayBusy<10)&&this.isBusy(g)){this._private.delayBusy++;c.setTimeout(this.proxy(function(){this._delayBusy.call(this,g,h);this._private.delayBusy--}),10);return}h.apply(this)},_dataSource:function(h){var j=this._instance.options.dataSource;if(j){var i=this.itemData(h);if(i&&i.source&&j[i.source]){return j[i.source]}var g;do{g=this.parent(h);i=this.itemData(g);if(i&&i.source&&j[i.source]){return j[i.source]}}while(g.length)}return this._instance.options.ajax},ajaxLoad:function(h,g){if(h&&this.isBusy(h)){this._delayBusy(h,function(){this.ajaxLoad(h,g)});return}g=this._options(g,function(){this._loading(h);this._trigger(h,"loaded",g)},function(){this._loading(h);this._trigger(h,"loadfail",g)},function(){this._loading(h);this._trigger(h,"wasloaded",g)});if(!h||this.isInode(h)){this._instance.queue.push(function(i){if(!this._trigger(h,"beforeload",g)){this._fail(h,g);i();return}this._loading(h,true);if(this.wasLoad(h)){this._notify(h,g);i();return}var j=d.extend({},this._dataSource(h));this._instance.options.ajaxHook.call(this,h,j);j.success=this.proxy(function(k){if(k&&(k instanceof Array)&&k.length){var l=function(){if(this.wasLoad(h)){this._notify(h,g);i()}else{this._createBranch(h,this._inner(g,{success:function(){this._success(h,g);i()},fail:function(){this._fail(h,g);i()},itemData:k}))}};if(!h||this.isInode(h)){l.apply(this)}else{this.setInode(h,this._inner(g,{success:l,fail:g.fail}))}}else{var l=function(){this._fail(h,g);i()};if(!h||this.isLeaf(h)){l.apply(this)}else{this.setLeaf(h,this._inner(g,{success:l,fail:l}))}}});j.error=this.proxy(function(){this._fail(h,g);i()});d.ajax(j)},true)}else{this._fail(h,g)}},loadFrom:function(h,g){if(h&&this.isBusy(h)){this._delayBusy(h,function(){this.loadFrom(h,g)});return}g=this._options(g,function(){this._loading(h);this._trigger(h,"loaded",g)},function(){this._loading(h);this._trigger(h,"loadfail",g)},function(){this._loading(h);this._trigger(h,"wasloaded",g)});if(!h||this.isInode(h)){if(!this._trigger(h,"beforeload",g)){this._fail(h,g);return}this._loading(h,true);if(this.wasLoad(h)){this._notify(h,g);return}if(g.itemData&&(g.itemData instanceof Array)&&g.itemData.length){var i=function(){if(this.wasLoad(h)){this._notify(h,g)}else{this._createBranch(h,g)}};if(!h||this.isInode(h)){i.apply(this)}else{this.setInode(h,this._inner(g,{success:i,fail:g.fail}))}}else{if(!h||this.isLeaf(h)){this._fail(h,g)}else{this.setLeaf(h,this._inner(g,{success:g.fail,fail:g.fail}))}}}else{this._fail(h,g)}},unload:function(j,g){g=this._options(g,function(){this._loading(j);this._trigger(j,"unloaded",g)},function(){this._loading(j);this._trigger(j,"unloadfail",g)},function(){this._loading(j);this._trigger(j,"notloaded",g)});if(!j||this.isInode(j)){if(!this._trigger(j,"beforeunload",g)){this._fail(j,g);return}this._loading(j,true);if(!this.wasLoad(j)){this._notify(j,g);return}var i=false;var h=this.children(j,true,true);h.each(this.proxy(function(l){var m=d(l);if(this.isInode(m)){if(this.isOpen(m)){if(!this._trigger(m,"beforeclose",g)){i=true;return false}}if(this.wasLoad(m)){if(!this._trigger(m,"beforeunload",g)){i=true;return false}}}if(!this._trigger(m,"beforeremove",g)){i=true;return false}},true));if(i){this._fail(j,g);return}var k=function(){h.each(this.proxy(function(l){var m=d(l);if(this.isInode(m)){if(this.isOpen(m)){this._trigger(m,"closed",g)}if(this.wasLoad(m)){this._trigger(m,"unloaded",g)}}this._trigger(m,"removed",g)},true))};if(j){if(this.isOpen(j)){this.close(j,this._inner(g,{success:function(){k.call(this);this._removeContainer(j);this._success(j,g)},fail:g.fail}))}else{k.call(this);this._removeContainer(j);this._success(j,g)}}else{this._animate(j,false,!this._instance.options.animateRoot||g.unanimated,function(){k.call(this);this._removeContainer();this._success(j,g)})}}else{this._fail(j,g)}},remove:function(i,g){if(this.isItem(i)){if(this.hasSiblings(i)){g=this._options(g,function(){if(this.isOpenPath(i)){a.removeClass(i[0],"aciTreeVisible");this._setOddEven(i)}this._trigger(i,"removed",g)},"removefail",null,i);if(!this._trigger(i,"beforeremove",g)){this._fail(i,g);return}if(this.wasLoad(i)){this.unload(i,this._inner(g,{success:function(){this._success(i,g);this._removeItem(i)},fail:g.fail}))}else{this._success(i,g);this._removeItem(i)}}else{var h=this.parent(i);if(h.length){this.setLeaf(h,g)}else{this.unload(null,g)}}}else{this._trigger(i,"removefail",g);this._fail(i,g)}},_openChildren:function(i,h){if(h.expand){var g=this._instance.queue;this.inodes(this.children(i)).each(function(){var j=d(this);g.push(function(k){this.open(j,this._inner(h));k()})});g.push(function(j){this._success(i,h);j()})}else{this._success(i,h)}},_openItem:function(h,g){if(!g.unanimated&&!this.isVisible(h)){g.unanimated=true}if(g.unique){this.closeOthers(h);g.unique=false}this._coreDOM.toggle(h,true);this._setOddEvenChildren(h);this._animate(h,true,g.unanimated,function(){this._openChildren(h,g)})},open:function(h,g){g=this._options(g,function(){if(this.isOpenPath(h)){this._updateVisible(h);this._setOddEven(h)}this._trigger(h,"opened",g)},"openfail","wasopened",h);if(this.isInode(h)){if(!this._trigger(h,"beforeopen",g)){this._fail(h,g);return}if(this.isOpen(h)){g.success=g.notify;this._openChildren(h,g)}else{if(this.wasLoad(h)){this._openItem(h,g)}else{this.ajaxLoad(h,this._inner(g,{success:function(){this._openItem(h,g)},fail:g.fail}))}}}else{this._fail(h,g)}},_closeChildren:function(i,h){if(this._instance.options.empty){h.unanimated=true;this.unload(i,h)}else{if(h.collapse){var g=this._instance.queue;this.inodes(this.children(i)).each(function(){var j=d(this);g.push(function(k){this.close(j,this._inner(h,{unanimated:true}));k()})});g.push(function(j){this._success(i,h);j()})}else{this._success(i,h)}}},_closeItem:function(h,g){if(!g.unanimated&&!this.isVisible(h)){g.unanimated=true}this._coreDOM.toggle(h,false);this._animate(h,false,g.unanimated,function(){this._closeChildren(h,g)})},close:function(h,g){g=this._options(g,function(){if(this.isOpenPath(h)){this._updateVisible(h);this._setOddEven(h)}this._trigger(h,"closed",g)},"closefail","wasclosed",h);if(this.isInode(h)){if(!this._trigger(h,"beforeclose",g)){this._fail(h,g);return}if(this.isOpen(h)){this._closeItem(h,g)}else{if(this.wasLoad(h)){g.success=g.notify;this._closeChildren(h,g)}else{this._notify(h,g)}}}else{this._fail(h,g)}},_updateVisible:function(g){if(this.isOpenPath(g)){if(!this.isHidden(g)){a.addClass(g[0],"aciTreeVisible");if(this.isOpen(g)){a.children(g[0],false,this.proxy(function(h){if(!a.hasClass(h,"aciTreeVisible")){this._updateVisible(d(h))}}))}else{a.children(g[0],true,function(h){return a.removeClass(h,"aciTreeVisible")?true:null})}}}else{if(a.removeClass(g[0],"aciTreeVisible")){a.children(g[0],true,function(h){return a.removeClass(h,"aciTreeVisible")?true:null})}}},closeOthers:function(j,i){i=this._options(i);if(this.isItem(j)){var g=this._instance.queue;var h=j.add(this.path(j)).add(this.children(j,true));this.inodes(this.children(null,true,true),true).not(h).each(function(){var k=d(this);g.push(function(l){this.close(k,this._inner(i));l()})});g.push(function(k){this._success(j,i);k()})}else{this._fail(j,i)}},toggle:function(h,g){g=this._options(g,"toggled","togglefail",null,h);if(this.isInode(h)){if(!this._trigger(h,"beforetoggle",g)){this._fail(h,g);return}if(this.isOpen(h)){this.close(h,g)}else{this.open(h,g)}}else{this._fail(h,g)}},path:function(i,g){if(i){var h=i[0],j=[];while(h=a.parent(h)){j.push(h)}return g?d(j):d(j.reverse())}return d([])},isVisible:function(k,h){if(k&&this.isOpenPath(k)){var j=this._instance.jQuery[0].getBoundingClientRect();var i=a.childrenByClass(k[0],"aciTreeItem");var m=i.getBoundingClientRect();var g=d(i).outerHeight(true);var l=h?this._instance.jQuery.innerHeight()/2:0;if((m.bottom-g<j.top+l)||(m.top+g>j.bottom-l)){return false}return true}return false},openPath:function(i,h){h=this._options(h);if(this.isItem(i)){var g=this._instance.queue;this.inodes(this.path(i),false).each(function(){var j=d(this);g.push(function(k){this.open(j,this._inner(h));k()})});g.push(function(j){this._success(i,h);j()})}else{this._fail(i,h)}},isOpenPath:function(h){var g=this.parent(h);return g.length?this.isOpen(g)&&a.hasClass(g[0],"aciTreeVisible"):true},_speedFraction:function(i,g,j){if((j<g)&&g){var h=parseInt(i);if(isNaN(h)){switch(i){case"slow":h=600;break;case"medium":h=400;break;case"fast":h=200;break;default:return i}}return h*j/g}return i},setVisible:function(h,g){g=this._options(g,"visible","visiblefail","wasvisible",h);if(this.isItem(h)){if(!this._trigger(h,"beforevisible",g)){this._fail(h,g);return}if(this.isVisible(h)){this._notify(h,g);return}var i=function(){var l=this._instance.jQuery[0].getBoundingClientRect();var k=a.childrenByClass(h[0],"aciTreeItem");var o=k.getBoundingClientRect();var j=d(k).outerHeight(true);var n=g.center?this._instance.jQuery.innerHeight()/2:0;if(o.bottom-j<l.top+n){var m=l.top+n-o.bottom+j;if(!g.unanimated&&this._instance.options.view){this._instance.jQuery.stop(true).animate({scrollTop:this._instance.jQuery.scrollTop()-m},{duration:this._speedFraction(this._instance.options.view.duration,l.bottom-l.top,m),easing:this._instance.options.view.easing,complete:this.proxy(function(){this._success(h,g)})})}else{this._instance.jQuery.stop(true).get(0).scrollTop=this._instance.jQuery.scrollTop()-m;this._success(h,g)}}else{if(o.top+j>l.bottom-n){var m=o.top-l.bottom+n+j;if(!g.unanimated&&this._instance.options.view){this._instance.jQuery.stop(true).animate({scrollTop:this._instance.jQuery.scrollTop()+m},{duration:this._speedFraction(this._instance.options.view.duration,l.bottom-l.top,m),easing:this._instance.options.view.easing,complete:this.proxy(function(){this._success(h,g)})})}else{this._instance.jQuery.stop(true).get(0).scrollTop=this._instance.jQuery.scrollTop()+m;this._success(h,g)}}else{this._success(h,g)}}};if(this.hasParent(h)){this.openPath(h,this._inner(g,{success:i,fail:g.fail}))}else{i.apply(this)}}else{this._fail(h,g)}},hasParent:function(g){return this.parent(g).length>0},parent:function(g){return g?d(a.parent(g[0])):d([])},topParent:function(g){return this.path(g).eq(0)},_createBranch:function(l,i){var k=0;var j=function(o){var n;for(var p=0;p<o.length;p++){n=o[p];if(n.branch&&(n.branch instanceof Array)&&n.branch.length){j(n.branch)}}k++};j(i.itemData);var h=0;var g=this.proxy(function(){h++;if(h>=k){this._success(l,i)}});var m=this.proxy(function(o,n){if(o){a.addRemoveClass(o[0],"aciTreeInode","aciTreeInodeMaybe")}this.append(o,this._inner(i,{success:function(s,q){var p;for(var r=0;r<q.itemData.length;r++){p=q.itemData[r];if(p.branch&&(p.branch instanceof Array)&&p.branch.length){m(q.items.eq(r),p.branch)}if(p.open){this.open(q.items.eq(r),this._inner(q,{itemData:null,items:null}))}}g()},fail:i.fail,itemData:n}))});m(l,i.itemData)},_getFirstLast:function(g){if(!g){g=this._instance.jQuery}return d(a.withAnyClass(a.children(g[0]),["aciTreeFirst","aciTreeLast"]))},_setFirstLast:function(h,g){if(g){a.removeListClass(g.toArray(),["aciTreeFirst","aciTreeLast"])}if(this.hasChildren(h)){a.addClass(this.first(h)[0],"aciTreeFirst");a.addClass(this.last(h)[0],"aciTreeLast")}},_setOddEven:function(g){var k;if(this._instance.jQuery[0].getElementsByClassName){k=this._instance.jQuery[0].getElementsByClassName("aciTreeVisible");k=k?c.Array.prototype.slice.call(k):[]}else{k=d(a.children(this._instance.jQuery[0],true,function(l){return this.hasClass(l,"aciTreeVisible")?true:null}))}var i=true;if(k.length){var h=0;if(g){g.each(function(){if(k.indexOf){var m=k.indexOf(this);if(m!=-1){h=c.Math.min(m,h)}}else{for(var l=0;l<k.length;l++){if(k[l]===this){h=c.Math.min(l,h);break}}}});h=c.Math.max(h-1,0)}if(h>0){var j=k[h];if(a.hasClass(j,"aciTreOdd")){i=false}k=k.slice(h+1)}}this._coreDOM.oddEven(d(k),i)},_setOddEvenChildren:function(h){var i=a.hasClass(h[0],"aciTreeOdd");var g=this.children(h);this._coreDOM.oddEven(g,!i)},_itemHook:function(h,i,g,j){if(this._instance.options.itemHook){this._instance.options.itemHook.apply(this,arguments)}},_createItem:function(h,g){if(this._private.itemClone[g]){var r=this._private.itemClone[g].cloneNode(true);var n=r.firstChild;for(var j=0;j<g;j++){n=n.firstChild}n=n.firstChild.lastChild.firstChild;var s=n.nextSibling}else{var r=c.document.createElement("LI");r.setAttribute("tabindex",-1);r.setAttribute("role","treeitem");r.setAttribute("aria-selected",false);var u=c.document.createElement("DIV");r.appendChild(u);u.className="aciTreeLine";var q=u,p;for(var j=0;j<g;j++){p=c.document.createElement("DIV");q.appendChild(p);p.className="aciTreeBranch aciTreeLevel"+j;q=p}var o=c.document.createElement("DIV");q.appendChild(o);o.className="aciTreeEntry";var k=c.document.createElement("SPAN");o.appendChild(k);k.className="aciTreeButton";var m=c.document.createElement("SPAN");k.appendChild(m);m.className="aciTreePush";m.appendChild(c.document.createElement("SPAN"));var t=c.document.createElement("SPAN");o.appendChild(t);t.className="aciTreeItem";var n=c.document.createElement("SPAN");t.appendChild(n);var s=c.document.createElement("SPAN");t.appendChild(s);s.className="aciTreeText";this._private.itemClone[g]=r.cloneNode(true)}r.className="aciTreeLi"+(h.inode||(h.inode===null)?(h.inode||(h.branch&&h.branch.length)?" aciTreeInode":" aciTreeInodeMaybe"):" aciTreeLeaf")+" aciTreeLevel"+g+(h.disabled?" aciTreeDisabled":"");r.setAttribute("aria-level",g+1);if(h.icon){if(h.icon instanceof Array){n.className="aciTreeIcon "+h.icon[0];n.style.backgroundPosition=h.icon[1]+"px "+h.icon[2]+"px"}else{n.className="aciTreeIcon "+h.icon}}else{n.parentNode.removeChild(n)}s.innerHTML=h.label;var l=d(r);l.data("itemData"+this._instance.nameSpace,d.extend({},h,{branch:h.branch&&h.branch.length}));return l},_removeItem:function(h){var g=this.parent(h);h.remove();this._setFirstLast(g.length?g:null)},_createItems:function(m,p,h,k,g,r){var o=[],n=c.document.createDocumentFragment();var j=new this._task(this._instance.queue,function(i){o=d(o);if(o.length){if(m){m[0].appendChild(n)}else{if(p){p[0].parentNode.insertBefore(n,p[0])}else{if(h){h[0].parentNode.insertBefore(n,h[0].nextSibling)}}}}r.call(this,o);i()});if(k){this._loader(true);var q;if(m){q=this.itemFrom(m)}else{if(p){q=this.parent(p)}else{if(h){q=this.parent(h)}}}if(k instanceof Array){for(var l=0;l<k.length;l++){(function(i){j.push(function(s){var t=this._createItem(i,g);this._itemHook(q,t,i,g);n.appendChild(t[0]);o.push(t[0]);s()})})(k[l])}}else{j.push(function(i){var s=this._createItem(k,g);this._itemHook(q,s,k,g);n.appendChild(s[0]);o.push(s[0]);i()})}}j.push(function(i){i()})},_createContainer:function(h){if(!h){h=this._instance.jQuery}var g=a.container(h[0]);if(!g){var g=c.document.createElement("UL");g.setAttribute("role","group");g.className="aciTreeUl";g.style.display="none";h[0].appendChild(g)}return d(g)},_removeContainer:function(h){if(!h){h=this._instance.jQuery}var g=a.container(h[0]);g.parentNode.removeChild(g)},append:function(j,h){h=this._options(h,"appended","appendfail",null,j);if(j){if(this.isInode(j)){if(!this._trigger(j,"beforeappend",h)){this._fail(j,h);return}var g=this._createContainer(j);var i=this.last(j);this._createItems(g,null,null,h.itemData,this.level(j)+1,function(k){if(k.length){a.addRemoveClass(j[0],"aciTreeInode","aciTreeInodeMaybe");this._setFirstLast(j,i);if(this.isHidden(j)){a.addListClass(k.toArray(),"aciTreeHidden")}else{if(this.isOpenPath(j)&&this.isOpen(j)){a.addListClass(k.toArray(),"aciTreeVisible");this._setOddEven(k.first())}}k.each(this.proxy(function(l){this._trigger(d(l),"added",h)},true))}else{if(!this.hasChildren(j,true)){g.remove()}}h.items=k;this._success(j,h)})}else{this._fail(j,h)}}else{if(!this._trigger(j,"beforeappend",h)){this._fail(j,h);return}var g=this._createContainer();var i=this.last();this._createItems(g,null,null,h.itemData,0,function(k){if(k.length){this._setFirstLast(null,i);a.addListClass(k.toArray(),"aciTreeVisible");this._setOddEven();k.each(this.proxy(function(l){this._trigger(d(l),"added",h)},true));this._animate(null,true,!this._instance.options.animateRoot||h.unanimated)}else{if(!this.hasChildren(null,true)){g.remove()}}h.items=k;this._success(j,h)})}},before:function(i,g){g=this._options(g,"before","beforefail",null,i);if(this.isItem(i)){if(!this._trigger(i,"beforebefore",g)){this._fail(i,g);return}var h=this.prev(i);this._createItems(null,i,null,g.itemData,this.level(i),function(k){if(k.length){if(!h.length){a.removeClass(i[0],"aciTreeFirst");a.addClass(k.first()[0],"aciTreeFirst")}var j=this.parent(i);if(j.length&&this.isHidden(j)){a.addListClass(k.toArray(),"aciTreeHidden")}else{if(this.isOpenPath(i)){a.addListClass(k.toArray(),"aciTreeVisible");this._setOddEven(k.first())}}k.each(this.proxy(function(l){this._trigger(d(l),"added",g)},true))}g.items=k;this._success(i,g)})}else{this._fail(i,g)}},after:function(i,g){g=this._options(g,"after","afterfail",null,i);if(this.isItem(i)){if(!this._trigger(i,"beforeafter",g)){this._fail(i,g);return}var h=this.next(i);this._createItems(null,null,i,g.itemData,this.level(i),function(k){if(k.length){if(!h.length){a.removeClass(i[0],"aciTreeLast");a.addClass(k.last()[0],"aciTreeLast")}var j=this.parent(i);if(j.length&&this.isHidden(j)){a.addListClass(k.toArray(),"aciTreeHidden")}else{if(this.isOpenPath(i)){a.addListClass(k.toArray(),"aciTreeVisible");this._setOddEven(k.first())}}k.each(this.proxy(function(l){this._trigger(d(l),"added",g)},true))}g.items=k;this._success(i,g)})}else{this._fail(i,g)}},itemFrom:function(g){if(g){var h=d(g);if(h[0]===this._instance.jQuery[0]){return d([])}else{return d(a.parentFrom(h[0]))}}return d([])},children:function(h,g,i){return d(a.children(h?h[0]:this._instance.jQuery[0],g,i?null:function(j){return this.hasClass(j,"aciTreeHidden")?null:true}))},visible:function(h,g){var l=a.withClass(h.toArray(),"aciTreeVisible");if(g){var k=[];for(var j=0;j<l.length;j++){if(this.isVisible(d(l[j]))){k.push(l[j])}}return d(k)}return d(l)},inodes:function(g,h){if(h!==f){if(h){return d(a.withClass(g.toArray(),"aciTreeOpen"))}else{return d(a.withAnyClass(g.toArray(),["aciTreeInode","aciTreeInodeMaybe"],"aciTreeOpen"))}}return d(a.withAnyClass(g.toArray(),["aciTreeInode","aciTreeInodeMaybe"]))},leaves:function(g){return d(a.withClass(g.toArray(),"aciTreeLeaf"))},isInode:function(g){return g&&a.hasAnyClass(g[0],["aciTreeInode","aciTreeInodeMaybe"])},isLeaf:function(g){return g&&a.hasClass(g[0],"aciTreeLeaf")},wasLoad:function(g){if(g){return a.container(g[0])!==null}return a.container(this._instance.jQuery[0])!==null},setInode:function(h,g){g=this._options(g,"inodeset","inodefail","wasinode",h);if(this.isItem(h)){if(!this._trigger(h,"beforeinode",g)){this._fail(h,g);return}if(this.isLeaf(h)){this._coreDOM.inode(h,true);this._success(h,g)}else{this._notify(h,g)}}else{this._fail(h,g)}},setLeaf:function(h,g){g=this._options(g,"leafset","leaffail","wasleaf",h);if(this.isItem(h)){if(!this._trigger(h,"beforeleaf",g)){this._fail(h,g);return}if(this.isInode(h)){var i=function(){this._coreDOM.leaf(h);this._success(h,g)};if(this.wasLoad(h)){this.unload(h,this._inner(g,{success:i,fail:g.fail}))}else{i.apply(this)}}else{this._notify(h,g)}}else{this._fail(h,g)}},addIcon:function(i,g){g=this._options(g,"iconadded","addiconfail","wasicon",i);if(this.isItem(i)){if(!this._trigger(i,"beforeaddicon",g)){this._fail(i,g);return}var k=this.itemData(i);g.oldIcon=k.icon;var h=i.children(".aciTreeLine").find(".aciTreeItem");var j=h.children(".aciTreeIcon");if(j.length&&k.icon&&(g.icon.toString()==k.icon.toString())){this._notify(i,g)}else{if(g.icon instanceof Array){if(j.length){j.attr("class","aciTreeIcon "+g.icon[0]).css("background-position",g.icon[1]+"px "+g.icon[2]+"px")}else{h.prepend('<div class="aciTreeIcon '+g.icon[0]+'" style="background-position:'+g.icon[1]+"px "+g.icon[2]+'px"></div>')}}else{if(j.length){j.attr("class","aciTreeIcon "+g.icon)}else{h.prepend('<div class="aciTreeIcon '+g.icon+'"></div>')}}k.icon=g.icon;this._success(i,g)}}else{this._fail(i,g)}},removeIcon:function(i,g){g=this._options(g,"iconremoved","removeiconfail","noticon",i);if(this.isItem(i)){if(!this._trigger(i,"beforeremoveicon",g)){this._fail(i,g);return}var k=this.itemData(i);g.oldIcon=k.icon;var h=i.children(".aciTreeLine").find(".aciTreeItem");var j=h.children(".aciTreeIcon");if(j.length){j.remove();k.icon=null;this._success(i,g)}else{this._notify(i,g)}}else{this._fail(i,g)}},hasIcon:function(g){return !!this.getIcon(g)},getIcon:function(g){var h=this.itemData(g);return h?h.icon:null},setLabel:function(h,g){g=this._options(g,"labelset","labelfail","waslabel",h);if(this.isItem(h)){if(!this._trigger(h,"beforelabel",g)){this._fail(h,g);return}var i=this.itemData(h);g.oldLabel=i.label;if(g.label==g.oldLabel){this._notify(h,g)}else{h.children(".aciTreeLine").find(".aciTreeText").html(g.label);i.label=g.label;this._success(h,g)}}else{this._fail(h,g)}},disable:function(h,g){g=this._options(g,"disabled","disablefail","wasdisabled",h);if(this.isItem(h)){if(!this._trigger(h,"beforedisable",g)){this._fail(h,g);return}if(this.isDisabled(h)){this._notify(h,g)}else{h.addClass("aciTreeDisabled");this._success(h,g)}}else{this._fail(h,g)}},isDisabled:function(g){return g&&g.hasClass("aciTreeDisabled")},isDisabledPath:function(g){return this.path(g).is(".aciTreeDisabled")},disabled:function(g){return g.filter(".aciTreeDisabled")},enable:function(h,g){g=this._options(g,"enabled","enablefail","wasenabled",h);if(this.isItem(h)){if(!this._trigger(h,"beforeenable",g)){this._fail(h,g);return}if(this.isDisabled(h)){h.removeClass("aciTreeDisabled");this._success(h,g)}else{this._notify(h,g)}}else{this._fail(h,g)}},isEnabled:function(g){return g&&!g.hasClass("aciTreeDisabled")},isEnabledPath:function(g){return !this.path(g).is(".aciTreeDisabled")},enabled:function(g){return g.not(".aciTreeDisabled")},hide:function(i,g){g=this._options(g,"hidden","hidefail","washidden",i);if(this.isItem(i)){if(!this._trigger(i,"beforehide",g)){this._fail(i,g);return}if(this.isHidden(i)){this._notify(i,g)}else{i.removeClass("aciTreeVisible").addClass("aciTreeHidden");this.children(i,true).removeClass("aciTreeVisible").addClass("aciTreeHidden");var h=this.parent(i);this._setFirstLast(h.length?h:null,i);this._setOddEven(i);this._success(i,g)}}else{this._fail(i,g)}},isHidden:function(g){return g&&g.hasClass("aciTreeHidden")},isHiddenPath:function(h){var g=this.parent(h);return g.length?g.hasClass("aciTreeHidden"):false},_updateHidden:function(g){if(this.isHiddenPath(g)){if(!this.isHidden(g)){g.addClass("aciTreeHidden");this._updateVisible(g)}}else{this._updateVisible(g)}},hidden:function(g){return g.filter(".aciTreeHidden")},_showHidden:function(h){var g=null;this.path(h).add(h).each(this.proxy(function(i){var j=d(i);if(this.isHidden(j)){j.removeClass("aciTreeHidden");if(this.isOpenPath(j)&&(!g||this.isOpen(g))){j.addClass("aciTreeVisible")}this._setFirstLast(g,this._getFirstLast(g))}g=j},true))},show:function(i,g){g=this._options(g,"shown","showfail","wasshown",i);if(this.isItem(i)){if(!this._trigger(i,"beforeshow",g)){this._fail(i,g);return}if(this.isHidden(i)){this._showHidden(i);var h=this.topParent(i);this._setOddEven(h.length?h:i);this._success(i,g)}else{this._notify(i,g)}}else{this._fail(i,g)}},isOpen:function(g){return g&&g.hasClass("aciTreeOpen")},isClosed:function(g){return g&&!g.hasClass("aciTreeOpen")},hasChildren:function(g,h){return this.children(g,null,h).length>0},hasSiblings:function(g,h){return this.siblings(g,h).length>0},hasPrev:function(g,h){return this.prev(g,h).length>0},hasNext:function(g,h){return this.next(g,h).length>0},siblings:function(g,h){return g?g.siblings(".aciTreeLi"+(h?"":":not(.aciTreeHidden)")):d([])},prev:function(g,h){return g?(h?g.prev(".aciTreeLi"):g.prevAll(".aciTreeLi:not(.aciTreeHidden):first")):d([])},next:function(g,h){return g?(h?g.next(".aciTreeLi"):g.nextAll(".aciTreeLi:not(.aciTreeHidden):first")):d([])},level:function(g){var h=-1;if(this.isItem(g)){while(g.hasClass("aciTreeLi")){g=g.parent().parent();h++}}return h},getId:function(g){var h=this.itemData(g);return h?h.id:null},itemData:function(g){return g?g.data("itemData"+this._instance.nameSpace):null},setId:function(h,g){g=this._options(g,"idset","idfail","wasid",h);if(this.isItem(h)){if(!this._trigger(h,"beforeid",g)){this._fail(h,g);return}var i=this.itemData(h);g.oldId=i.id;if(g.id==g.oldId){this._notify(h,g)}else{i.id=g.id;this._success(h,g)}}else{this._fail(h,g)}},getIndex:function(g){return g?g.parent().children(".aciTreeLi").index(g):null},setIndex:function(i,g){g=this._options(g,"indexset","indexfail","wasindex",i);if(this.isItem(i)){var k=this.getIndex(i);var j=this.siblings(i);if((g.index!=k)&&!j.length){this._fail(i,g);return}if(!this._trigger(i,"beforeindex",g)){this._fail(i,g);return}g.oldIndex=k;if(g.index==k){this._notify(i,g)}else{if(g.index<1){j.first().before(i)}else{if(g.index>=j.length){j.last().after(i)}else{j.eq(g.index).before(i)}}var h=this.parent(i);this._setFirstLast(h.length?h:null,i.add([j.get(0),j.get(-1)]));this._setOddEven(h);this._success(i,g)}}else{this._fail(i,g)}},getLabel:function(g){var h=this.itemData(g);return h?h.label:null},isItem:function(g){return g&&g.hasClass("aciTreeLi")},_animate:function(j,k,i,l){if(!j){j=this._instance.jQuery}if(!i){var h=k?this._instance.options.show:this._instance.options.hide;if(h){var g=j.children(".aciTreeUl");if(g.length){g.stop(true,true).animate(h.props,{duration:h.duration,easing:h.easing,complete:l?this.proxy(l):null})}else{if(l){l.apply(this)}}return}}j.children(".aciTreeUl").stop(true,true).toggle(k);if(l){l.apply(this)}},first:function(g,h){if(!g){g=this._instance.jQuery}return g.children(".aciTreeUl").children(".aciTreeLi"+(h?"":":not(.aciTreeHidden)")+":first")},isFirst:function(h,i){if(h){var g=this.parent(h);return this.first(g.length?g:null,i).is(h)}return false},last:function(g,h){if(!g){g=this._instance.jQuery}return g.children(".aciTreeUl").children(".aciTreeLi"+(h?"":":not(.aciTreeHidden)")+":last")},isLast:function(h,i){if(h){var g=this.parent(h);return this.last(g.length?g:null,i).is(h)}return false},isBusy:function(g){if(g){return a.hasClass(g[0],"aciTreeLoad")}else{return this._instance.queue.busy()}},_loading:function(g,h){if(g){a.toggleClass(g[0],"aciTreeLoad",h);if(h){g[0].setAttribute("aria-busy",true)}else{g[0].removeAttribute("aria-busy")}}else{if(h){this._loader(h)}}},_loader:function(g){if(g||this.isBusy()){if(!this._private.loaderInterval){this._private.loaderInterval=c.setInterval(this.proxy(function(){this._loader()}),this._instance.options.loaderDelay)}this._instance.jQuery.addClass("aciTreeLoad");c.clearTimeout(this._private.loaderHide);this._private.loaderHide=c.setTimeout(this.proxy(function(){this._instance.jQuery.removeClass("aciTreeLoad")}),this._instance.options.loaderDelay*2)}},isChildren:function(h,g){if(!h){h=this._instance.jQuery}return g&&(h.has(g).length>0)},isImmediateChildren:function(h,g){if(!h){h=this._instance.jQuery}return g&&h.children(".aciTreeUl").children(".aciTreeLi").is(g)},sameParent:function(i,g){if(i&&g){var j=this.parent(i);var h=this.parent(g);return(!j.length&&!h.length)||(j.get(0)==h.get(0))}return false},sameTopParent:function(i,g){if(i&&g){var j=this.topParent(i);var h=this.topParent(g);return(!j.length&&!h.length)||(j.get(0)==h.get(0))}return false},_serialize:function(g,i){var h=this.itemData(g);if(this.isInode(g)){h.inode=true;if(this.wasLoad(g)){if(h.hasOwnProperty("open")){h.open=this.isOpen(g)}else{if(this.isOpen(g)){h.open=true}}h.branch=[];this.children(g,false,true).each(this.proxy(function(j){var k=this._serialize(d(j),i);if(i){k=i.call(this,d(j),{},k)}else{k=this._instance.options.serialize.call(this,d(j),{},k)}if(k){h.branch.push(k)}},true));if(!h.branch.length){h.branch=null}}else{if(h.hasOwnProperty("open")){h.open=false}if(h.hasOwnProperty("branch")){h.branch=null}}}else{if(h.hasOwnProperty("inode")){h.inode=false}if(h.hasOwnProperty("open")){h.open=null}if(h.hasOwnProperty("branch")){h.branch=null}}if(h.hasOwnProperty("disabled")){h.disabled=this.isDisabled(g)}else{if(this.isDisabled(g)){h.disabled=true}}return h},serialize:function(g,j,k){if(typeof j=="object"){if(g){var i=this._serialize(g,k);if(k){i=k.call(this,g,{},i)}else{i=this._instance.options.serialize.call(this,g,{},i)}return i}else{var h=[];this.children(null,false,true).each(this.proxy(function(l){var m=this._serialize(d(l),k);if(k){m=k.call(this,d(l),{},m)}else{m=this._instance.options.serialize.call(this,d(l),{},m)}if(m){h.push(m)}},true));return h}}return""},destroy:function(g){g=this._options(g);if(!this.wasInit()){this._trigger(null,"notinit",g);this._fail(null,g);return}if(this.isLocked()){this._trigger(null,"locked",g);this._fail(null,g);return}if(!this._trigger(null,"beforedestroy",g)){this._trigger(null,"destroyfail",g);this._fail(null,g);return}this._private.locked=true;this._instance.jQuery.addClass("aciTreeLoad").attr("aria-busy",true);this._instance.queue.destroy();this._destroyHook(false);this.unload(null,this._inner(g,{success:this.proxy(function(){c.clearTimeout(this._private.loaderHide);c.clearInterval(this._private.loaderInterval);this._private.itemClone={};this._destroyHook(true);this._instance.jQuery.unbind(this._instance.nameSpace).off(this._instance.nameSpace,".aciTreeButton").off(this._instance.nameSpace,".aciTreeLine");this._instance.jQuery.removeClass("aciTree"+this._instance.index+" aciTreeLoad").removeAttr("role aria-busy");this._private.locked=false;this._super();this._trigger(null,"destroyed",g);this._success(null,g)}),fail:function(){this._instance.jQuery.removeClass("aciTreeLoad");this._private.locked=false;this._trigger(null,"destroyfail",g);this._fail(null,g)}}))},_destroyHook:function(g){}};aciPluginClass.plugins.aciTree=aciPluginClass.aciPluginUi.extend(e,"aciTreeCore");aciPluginClass.publish("aciTree",b);var a=aciPluginClass.plugins.aciTree_dom})(jQuery,this);

// utils
(function(d,c,e){var a={filterHook:function(g,f,h){return f.length?h.test(c.String(this.getLabel(g))):true}};var b={__extend:function(){d.extend(this._instance,{filter:new this._queue(this,this._instance.options.queue)});this._instance.filter.destroy();this._super()},branch:function(g,j,h){var f=this._instance.queue;var i=this.proxy(function(l,n,k){var m=k?this.next(l):this.first(l);if(m.length){if(this.isInode(m)){if(this.wasLoad(m)){f.push(function(o){n.call(this,m);i(m,n);i(m,n,true);o()})}else{if(h){this.ajaxLoad(m,{success:function(){n.call(this,m);i(m,n);i(m,n,true)},fail:function(){i(m,n,true)}})}else{f.push(function(o){n.call(this,m);i(m,n,true);o()})}}}else{f.push(function(o){n.call(this,m);i(m,n,true);o()})}}});i(g,j)},swap:function(h){h=this._options(h,null,"swapfail",null,null);var g=h.item1;var f=h.item2;if(this.isItem(g)&&this.isItem(f)&&!this.isChildren(g,f)&&!this.isChildren(f,g)&&(g.get(0)!=f.get(0))){if(!this._trigger(null,"beforeswap",h)){this._fail(null,h);return}var k=this.prev(g);if(k.length){if(f.get(0)==k.get(0)){f.before(g)}else{g.insertAfter(f);f.insertAfter(k)}}else{var j=this.next(g);if(j.length){if(f.get(0)==j.get(0)){f.after(g)}else{g.insertAfter(f);f.insertBefore(j)}}else{var i=g.parent();g.insertAfter(f);i.append(f)}}this._updateLevel(g);var i=this.parent(g);this._setFirstLast(i.length?i:null,g);this._updateHidden(g);this._updateLevel(f);i=this.parent(f);this._setFirstLast(i.length?i:null,f);this._updateHidden(f);this._setOddEven(g.add(f));this._trigger(null,"swapped",h);this._success(null,h)}else{this._fail(null,h)}},_updateItemLevel:function(k,f,h){k.removeClass("aciTreeLevel"+f).addClass("aciTreeLevel"+h);var j=k.children(".aciTreeLine").find(".aciTreeEntry");if(f<h){for(var g=f;g<h;g++){j.wrap('<div class="aciTreeBranch aciTreeLevel'+g+'"></div>')}}else{if(f>h){for(var g=h;g<f;g++){j.unwrap()}}}},_updateChildLevel:function(h,f,g){this.children(h).each(this.proxy(function(i){var j=d(i);this._updateItemLevel(j,f,g);if(this.isInode(j)){this.children(j).each(this.proxy(function(k){this._updateChildLevel(d(k),f+1,g+1)},true))}},true))},_updateLevel:function(f){var h=this.level(f);var g=c.parseInt(f.attr("class").match(/aciTreeLevel[0-9]+/)[0].match(/[0-9]+/));if(h!=g){this._updateItemLevel(f,g,h);this._updateChildLevel(f,g+1,h+1)}},moveUp:function(g,f){f=this._options(f);f.index=c.Math.max(this.getIndex(g)-1,0);this.setIndex(g,f)},moveDown:function(g,f){f=this._options(f);f.index=c.Math.min(this.getIndex(g)+1,this.siblings(g).length);this.setIndex(g,f)},moveFirst:function(g,f){f=this._options(f);f.index=0;this.setIndex(g,f)},moveLast:function(g,f){f=this._options(f);f.index=this.siblings(g).length;this.setIndex(g,f)},moveBefore:function(i,f){f=this._options(f,null,"movefail","wasbefore",i);var j=f.before;if(this.isItem(i)&&this.isItem(j)&&!this.isChildren(i,j)&&(i.get(0)!=j.get(0))){if(!this._trigger(i,"beforemove",f)){this._fail(i,f);return}if(this.prev(j,true).get(0)==i.get(0)){this._notify(i,f)}else{var g=this.parent(i);var h=this.prev(i,true);if(!h.length){h=g.length?g:this.first()}i.insertBefore(j);if(g.length&&!this.hasChildren(g,true)){this.setLeaf(g)}this._updateLevel(i);this._setFirstLast(g.length?g:null);g=this.parent(i);this._setFirstLast(g.length?g:null,i.add(j));this._updateHidden(i);this._setOddEven(i.add(j).add(h));this._trigger(i,"moved",f);this._success(i,f)}}else{this._fail(i,f)}},moveAfter:function(i,f){f=this._options(f,null,"movefail","wasafter",i);var j=f.after;if(this.isItem(i)&&this.isItem(j)&&!this.isChildren(i,j)&&(i.get(0)!=j.get(0))){if(!this._trigger(i,"beforemove",f)){this._fail(i,f);return}if(this.next(j,true).get(0)==i.get(0)){this._notify(i,f)}else{var g=this.parent(i);var h=this.prev(i,true);if(!h.length){h=g.length?g:this.first()}i.insertAfter(j);if(g.length&&!this.hasChildren(g,true)){this.setLeaf(g)}this._updateLevel(i);this._setFirstLast(g.length?g:null);g=this.parent(i);this._setFirstLast(g.length?g:null,i.add(j));this._updateHidden(i);this._setOddEven(i.add(j).add(h));this._trigger(i,"moved",f);this._success(i,f)}}else{this._fail(i,f)}},asChild:function(h,f){f=this._options(f,null,"childfail",null,h);var g=f.parent;if(this.isItem(h)&&this.isItem(g)&&!this.isChildren(h,g)&&!this.hasChildren(g,true)&&(h.get(0)!=g.get(0))){if(!this._trigger(h,"beforechild",f)){this._fail(h,f);return}var i=function(){var k=this.parent(h);var l=this.prev(h);if(!l.length){l=k.length?k:this.first()}var j=this._createContainer(g);j.append(h);if(k.length&&!this.hasChildren(k,true)){this.setLeaf(k)}this._updateLevel(h);this._setFirstLast(k.length?k:null);this._setFirstLast(g.length?g:null,h);this._updateHidden(h);this._setOddEven(h.add(l));this._trigger(h,"childset",f);this._success(h,f)};if(this.isInode(g)){i.apply(this)}else{this.setInode(g,this._inner(f,{success:i,fail:f.fail}))}}else{this._fail(h,f)}},_search:function(n,k){var m=this.children(n);var o,f,h,q,l=false;for(var j=0,p=m.length;j<p;j++){o=m.eq(j);f=c.String(this.getId(o));h=f.length;if(h){if(f==k.substr(0,h)){q=o;l=k.length==h;break}}}if(q){if(!l){var g=this._search(q,k);if(g){return g}}return{item:q,exact:l}}else{return null}},searchId:function(j,h,f){f=this._options(f);var k=f.id;if(j){if(h){var i=this.proxy(function(l){var m=this._search(l,k);if(m){if(m.exact){this._success(m.item,f)}else{if(this.wasLoad(m.item)){this._fail(l,f)}else{this.ajaxLoad(m.item,this._inner(f,{success:function(){i(m.item)},fail:f.fail}))}}}else{this._fail(l,f)}});i()}else{var g=this._search(null,k);if(g&&g.exact){this._success(g.item,f)}else{this._fail(null,f)}}}else{var g=d();this._instance.jQuery.find(".aciTreeLi").each(this.proxy(function(l){if(k==this.getId(d(l))){g=d(l);return false}},true));if(g.length){this._success(g,f)}else{this._fail(null,f)}}},search:function(k,g){var j=[];g=this._options(g);var f=new this._task(new this._queue(this,this._instance.options.queue),function(l){if(j.length){g.results=d(j);this._success(d(j[0]),g)}else{this._fail(k,g)}l()});var i=this.proxy(function(l){this.children(l,false,true).each(this.proxy(function(n){if(g.callback){var m=g.callback.call(this,d(n),g.search);if(m){j.push(n)}else{if(m===null){return}}}else{if(this.getId(d(n))==g.search){j.push(n)}}if(this.isInode(d(n))){f.push(function(o){h(d(n));o()})}},true))});var h=this.proxy(function(l){if(this.wasLoad(l)){f.push(function(m){i(l);m()})}else{if(g.load){f.push(function(m){this.ajaxLoad(l,{success:function(){i(l);m()},fail:m})})}}});f.push(function(l){h(k);l()})},searchPath:function(h,f){f=this._options(f);var i=f.path;var g=this.proxy(function(j,k){this.search(j,{success:function(l){if(i.length){g(l,i.shift())}else{this._success(l,f)}},fail:function(){this._fail(j,f)},search:k,load:f.load,callback:function(m,l){return(this.getId(m)==l)?true:null}})});g(h,i.shift())},pathId:function(g,f){var h=this.path(g,f),i=[];h.each(this.proxy(function(j){i.push(this.getId(d(j)))},true));return i},_regexp:function(f){return new c.RegExp(c.String(f).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08"),"i")},filter:function(i,g){g=this._options(g,null,"filterfail",null,i);if(!i||this.isItem(i)){if(!this._trigger(i,"beforefilter",g)){this._fail(i,g);return}var h=c.String(g.search);var j=this._regexp(h);var l=null;this._instance.filter.init();var f=new this._task(this._instance.filter,function(m){this._instance.filter.destroy();g.first=l;this._setOddEven();this._trigger(i,"filtered",g);this._success(i,g);m()});var k=this.proxy(function(n){var m=this.children(n,false,true);var o=false;m.each(this.proxy(function(p){var q=d(p);if(this._instance.options.filterHook.call(this,q,h,j)){if(!l){l=q}o=true;q.removeClass("aciTreeHidden")}else{q.removeClass("aciTreeVisible").addClass("aciTreeHidden")}if(this.isInode(q)){f.push(function(r){k(q);r()})}},true));if(o){if(n&&this.isHidden(n)){this._showHidden(n)}if(!n||(this.isOpenPath(n)&&this.isOpen(n))){m.not(".aciTreeHidden").addClass("aciTreeVisible")}this._setFirstLast(n,this._getFirstLast(n))}});f.push(function(m){k(i);m()})}else{this._fail(i,g)}},_firstAll:function(f){f.call(this,this.first())},_lastAll:function(f,h,g){if(f){if(this.isInode(f)){if(this.wasLoad(f)){this._lastAll(this.last(f),h,g);return}else{if(g){this.ajaxLoad(f,{success:function(){this._lastAll(this.last(f),h,g)},fail:function(){h.call(this,f)}});return}}}h.call(this,f)}else{h.call(this,this.last())}},_nextAll:function(h,j,i){if(h){if(this.isInode(h)){if(this.wasLoad(h)){j.call(this,this.first(h));return}else{if(i){this.ajaxLoad(h,{success:function(){j.call(this,this.first(h))},fail:function(){this._nextAll(h,j,i)}});return}}}var g=this.next(h);if(g.length){j.call(this,g)}else{var f=this.proxy(function(m){var l=this.parent(m);if(l.length){var k=this.next(l);if(k.length){return k}else{return f(l)}}return null});j.call(this,f(h))}}else{j.call(this,this.first())}},_prevAll:function(h,j,i){if(h){var g=this.prev(h);if(g.length){if(this.isInode(g)){this._lastAll(g,j,i)}else{j.call(this,g)}}else{var f=this.parent(h);j.call(this,f.length?f:null)}}else{j.call(this,this.last())}},prevMatch:function(h,g,k){var i=this._regexp(g);this._instance.filter.init();var f=new this._task(this._instance.filter,function(l){this._instance.filter.destroy();l()});var j=function(l){f.push(function(m){this._prevAll(l,function(n){if(n){if(this._instance.options.filterHook.call(this,n,g,i)){k.call(this,n)}else{j(n)}}else{k.call(this,null)}m()})})};j(this.isItem(h)?h:null)},nextMatch:function(h,g,k){var i=this._regexp(g);this._instance.filter.init();var f=new this._task(this._instance.filter,function(l){this._instance.filter.destroy();l()});var j=function(l){f.push(function(m){this._nextAll(l,function(n){if(n){if(this._instance.options.filterHook.call(this,n,g,i)){k.call(this,n)}else{j(n)}}else{k.call(this,null)}m()})})};j(this.isItem(h)?h:null)}};aciPluginClass.plugins.aciTree=aciPluginClass.plugins.aciTree.extend(b,"aciTreeUtils");aciPluginClass.defaults("aciTree",a)})(jQuery,this);

// selectable
(function(d,c,e){var b={selectable:true,multiSelectable:false,fullRow:false,textSelection:false};var f={__extend:function(){d.extend(this._instance,{focus:false});d.extend(this._private,{blurTimeout:null,spinPoint:null});this._super()},hasFocus:function(){return this._instance.focus},_selectableInit:function(){if(this._instance.jQuery.attr("tabindex")===e){this._instance.jQuery.attr("tabindex",0)}if(!this._instance.options.textSelection){this._selectable(false)}this._instance.jQuery.bind("acitree"+this._private.nameSpace,function(k,i,j,g,h){switch(g){case"closed":var l=i.focused();if(i.isChildren(j,l)){i._focusOne(j)}i.children(j,true).each(i.proxy(function(m){var n=d(m);if(this.isSelected(n)){this.deselect(n)}},true));break}}).bind("focusin"+this._private.nameSpace,this.proxy(function(){c.clearTimeout(this._private.blurTimeout);if(!this.hasFocus()){this._instance.focus=true;this._instance.jQuery.addClass("aciTreeFocus");this._trigger(null,"focused")}})).bind("focusout"+this._private.nameSpace,this.proxy(function(){c.clearTimeout(this._private.blurTimeout);this._private.blurTimeout=c.setTimeout(this.proxy(function(){if(this.hasFocus()){this._instance.focus=false;this._instance.jQuery.removeClass("aciTreeFocus");this._trigger(null,"blurred")}}),10)})).bind("keydown"+this._private.nameSpace,this.proxy(function(j){if(!this.hasFocus()){return}var i=this.focused();if(i.length&&this.isBusy(i)){return false}var h=d([]);switch(j.which){case 65:if(this._instance.options.multiSelectable&&j.ctrlKey){var g=this.visible(this.enabled(this.children(null,true))).not(this.selected());g.each(this.proxy(function(k){this.select(d(k),{focus:false})},true));if(!this.focused().length){this._focusOne(this.visible(g,true).first())}j.preventDefault()}break;case 38:h=i.length?this._prev(i):this.first();break;case 40:h=i.length?this._next(i):this.first();break;case 37:if(i.length){if(this.isOpen(i)){h=i;this.close(i,{collapse:this._instance.options.collapse,expand:this._instance.options.expand,unique:this._instance.options.unique})}else{h=this.parent(i)}}else{h=this._first()}break;case 39:if(i.length){if(this.isInode(i)&&this.isClosed(i)){h=i;this.open(i,{collapse:this._instance.options.collapse,expand:this._instance.options.expand,unique:this._instance.options.unique})}else{h=this.first(i)}}else{h=this._first()}break;case 33:h=i.length?this._prevPage(i):this._first();break;case 34:h=i.length?this._nextPage(i):this._first();break;case 36:h=this._first();break;case 35:h=this._last();break;case 13:case 107:h=i;if(this.isInode(i)&&this.isClosed(i)){this.open(i,{collapse:this._instance.options.collapse,expand:this._instance.options.expand,unique:this._instance.options.unique})}break;case 27:case 109:h=i;if(this.isOpen(i)){this.close(i,{collapse:this._instance.options.collapse,expand:this._instance.options.expand,unique:this._instance.options.unique})}if(j.which==27){j.preventDefault()}break;case 32:h=i;if(this.isInode(i)&&!j.ctrlKey){this.toggle(i,{collapse:this._instance.options.collapse,expand:this._instance.options.expand,unique:this._instance.options.unique})}j.preventDefault();break;case 106:h=i;if(this.isInode(i)){this.open(i,{collapse:this._instance.options.collapse,expand:true,unique:this._instance.options.unique})}break}if(h.length){if(this._instance.options.multiSelectable&&!j.ctrlKey&&!j.shiftKey){this._unselect(this.selected().not(h))}if(!this.isVisible(h)){this.setVisible(h)}if(j.ctrlKey){if((j.which==32)&&this.isEnabled(h)){if(this.isSelected(h)){this.deselect(h)}else{this.select(h)}this._private.spinPoint=h}else{this._focusOne(h)}}else{if(j.shiftKey){this._shiftSelect(h)}else{if(!this.isSelected(h)&&this.isEnabled(h)){this.select(h)}else{this._focusOne(h)}this._private.spinPoint=h}}return false}}));this._fullRow(this._instance.options.fullRow);this._multiSelectable(this._instance.options.multiSelectable)},_fullRow:function(g){this._instance.jQuery.off(this._private.nameSpace,".aciTreeLine,.aciTreeItem").off(this._private.nameSpace,".aciTreeItem");this._instance.jQuery.on("mousedown"+this._private.nameSpace+" click"+this._private.nameSpace,g?".aciTreeLine,.aciTreeItem":".aciTreeItem",this.proxy(function(i){var h=this.itemFrom(i.target);if(!this.isVisible(h)){this.setVisible(h)}if(i.ctrlKey){if(i.type=="click"){if(this.isEnabled(h)){if(this.isSelected(h)){this.deselect(h);this._focusOne(h)}else{this.select(h)}}else{this._focusOne(h)}}}else{if(this._instance.options.multiSelectable&&i.shiftKey){this._shiftSelect(h)}else{if(this._instance.options.multiSelectable&&(!this.isSelected(h)||(i.type=="click"))){this._unselect(this.selected().not(h))}this._selectOne(h)}}if(!i.shiftKey){this._private.spinPoint=h}})).on("dblclick"+this._private.nameSpace,g?".aciTreeLine,.aciTreeItem":".aciTreeItem",this.proxy(function(i){var h=this.itemFrom(i.target);if(this.isInode(h)){this.toggle(h,{collapse:this._instance.options.collapse,expand:this._instance.options.expand,unique:this._instance.options.unique});return false}}))},_multiSelectable:function(g){if(g){this._instance.jQuery.attr("aria-multiselectable",true)}else{var h=this.focused();this._unselect(this.selected().not(h));this._instance.jQuery.removeAttr("aria-multiselectable")}},_shiftSelect:function(i){var l=this._private.spinPoint;if(!l||!d.contains(this._instance.jQuery.get(0),l.get(0))||!this.isOpenPath(l)){var l=this.focused()}if(l.length){var g=[i.get(0)],m=l.get(0),j=false,h=i.get(0);var k=this.visible(this.children(null,true));k.each(this.proxy(function(n){if(j){if(this.isEnabled(d(n))){g.push(n)}if((n==m)||(n==h)){return false}}else{if((n==m)||(n==h)){if(this.isEnabled(d(n))){g.push(n)}if((n==m)&&(n==h)){return false}j=true}}},true));this._unselect(this.selected().not(g));d(g).not(i).each(this.proxy(function(n){var o=d(n);if(!this.isSelected(o)){this.select(o,{focus:false})}},true))}this._selectOne(i)},_initHook:function(){if(this.extSelectable()){this._selectableInit()}this._super()},_itemHook:function(h,i,g,j){if(this.extSelectable()){this._selectableDOM.select(i,g.selected)}this._super(h,i,g,j)},_selectableDOM:{select:function(g,h){if(h){g.addClass("aciTreeSelected").attr("aria-selected",true)}else{g.removeClass("aciTreeSelected").attr("aria-selected",false)}},focus:function(g,h){if(h){g.addClass("aciTreeFocus").focus()}else{g.removeClass("aciTreeFocus")}}},_selectable:function(g){if(g){this._instance.jQuery.css({"-webkit-user-select":"text","-moz-user-select":"text","-ms-user-select":"text","-o-user-select":"text","user-select":"text"}).attr({unselectable:null,onselectstart:null}).unbind("selectstart"+this._private.nameSpace)}else{this._instance.jQuery.css({"-webkit-user-select":"none","-moz-user-select":"-moz-none","-ms-user-select":"none","-o-user-select":"none","user-select":"none"}).attr({unselectable:"on",onselectstart:"return false"}).bind("selectstart"+this._private.nameSpace,function(h){if(!d(h.target).is("input,textarea")){return false}})}},_first:function(){return d(a.first(this._instance.jQuery[0],function(g){return this.hasClass(g,"aciTreeVisible")?true:null}))},_last:function(){return d(a.last(this._instance.jQuery[0],function(g){return this.hasClass(g,"aciTreeVisible")?true:null}))},_prev:function(g){return d(a.prevAll(g[0],function(h){return this.hasClass(h,"aciTreeVisible")?true:null}))},_next:function(g){return d(a.nextAll(g[0],function(h){return this.hasClass(h,"aciTreeVisible")?true:null}))},_height:function(h){var g=h.children(".aciTreeLine").find(".aciTreeItem");return g.outerHeight(true)},_prevPage:function(j){var l=this._instance.jQuery.find(".aciTreeVisible");var k=this._instance.jQuery.height();var h=this._height(j);var i=j;var g=l.index(j);while((h<k)&&(g>0)){g--;i=l.eq(g);h+=this._height(i)}return i},_nextPage:function(j){var l=this._instance.jQuery.find(".aciTreeVisible");var k=this._instance.jQuery.height();var h=this._height(j);var i=j;var g=l.index(j);while((h<k)&&(g<l.length-1)){g++;i=l.eq(g);h+=this._height(i)}return i},_selectOne:function(g){if(this.isSelected(g)){this._focusOne(g)}else{if(this.isEnabled(g)){this.select(g)}else{this._focusOne(g)}}},_unselect:function(g){g.each(this.proxy(function(h){this.deselect(d(h))},true))},_focusOne:function(g){if(!this._instance.options.multiSelectable){this._unselect(this.selected().not(g))}if(!this.isFocused(g)){this.focus(g)}},select:function(i,h){h=this._options(h,"selected","selectfail","wasselected",i);if(this.extSelectable()&&this.isItem(i)){if(!this._trigger(i,"beforeselect",h)){this._fail(i,h);return}h.oldSelected=this.selected();if(!this._instance.options.multiSelectable){var g=h.oldSelected.not(i);this._selectableDOM.select(g,false);g.each(this.proxy(function(j){this._trigger(d(j),"deselected",h)},true))}if(this.isSelected(i)){this._notify(i,h)}else{this._selectableDOM.select(i,true);this._success(i,h)}if((h.focus===e)||h.focus){if(!this.isFocused(i)||h.focus){this.focus(i,this._inner(h))}}}else{this._fail(i,h)}},deselect:function(h,g){g=this._options(g,"deselected","deselectfail","notselected",h);if(this.extSelectable()&&this.isItem(h)){if(!this._trigger(h,"beforedeselect",g)){this._fail(h,g);return}if(this.isSelected(h)){this._selectableDOM.select(h,false);this._success(h,g)}else{this._notify(h,g)}}else{this._fail(h,g)}},focus:function(i,h){h=this._options(h,"focus","focusfail","wasfocused",i);if(this.extSelectable()&&this.isItem(i)){if(!this._trigger(i,"beforefocus",h)){this._fail(i,h);return}h.oldFocused=this.focused();var g=h.oldFocused.not(i);this._selectableDOM.focus(g,false);g.each(this.proxy(function(j){this._trigger(d(j),"blur",h)},true));if(this.isFocused(i)){this._notify(i,h)}else{this._selectableDOM.focus(i,true);this._success(i,h)}}else{this._fail(i,h)}},blur:function(h,g){g=this._options(g,"blur","blurfail","notfocused",h);if(this.extSelectable()&&this.isItem(h)){if(!this._trigger(h,"beforeblur",g)){this._fail(h,g);return}if(this.isFocused(h)){this._selectableDOM.focus(h,false);this._success(h,g)}else{this._notify(h,g)}}else{this._fail(h,g)}},selected:function(){return this._instance.jQuery.find(".aciTreeSelected")},_serialize:function(g,i){var h=this._super(g,i);if(h&&this.extSelectable()){if(h.hasOwnProperty("selected")){h.selected=this.isSelected(g)}else{if(this.isSelected(g)){h.selected=true}}}return h},isSelected:function(g){return g&&g.hasClass("aciTreeSelected")},focused:function(){return this._instance.jQuery.find(".aciTreeFocus")},isFocused:function(g){return g&&g.hasClass("aciTreeFocus")},extSelectable:function(){return this._instance.options.selectable},option:function(g,h){if(this.wasInit()&&!this.isLocked()){if((g=="selectable")&&(h!=this.extSelectable())){if(h){this._selectableInit()}else{this._selectableDone()}}if((g=="multiSelectable")&&(h!=this._instance.options.multiSelectable)){this._multiSelectable(h)}if((g=="fullRow")&&(h!=this._instance.options.fullRow)){this._fullRow(h)}if((g=="textSelection")&&(h!=this._instance.options.textSelection)){this._selectable(h)}}this._super(g,h)},_selectableDone:function(g){if(this._instance.jQuery.attr("tabindex")==0){this._instance.jQuery.removeAttr("tabindex")}if(!this._instance.options.textSelection){this._selectable(true)}this._instance.jQuery.unbind(this._private.nameSpace);this._instance.jQuery.off(this._private.nameSpace,".aciTreeLine,.aciTreeItem").off(this._private.nameSpace,".aciTreeItem");this._instance.jQuery.removeClass("aciTreeFocus").removeAttr("aria-multiselectable");this._instance.focus=false;this._private.spinPoint=null;if(!g){this._unselect(this.selected());var h=this.focused();if(h.length){this.blur(h)}}},_destroyHook:function(g){if(g){this._selectableDone(true)}this._super(g)}};aciPluginClass.plugins.aciTree=aciPluginClass.plugins.aciTree.extend(f,"aciTreeSelectable");aciPluginClass.defaults("aciTree",b);var a=aciPluginClass.plugins.aciTree_dom})(jQuery,this);

// checkbox (works best with selectable)
(function(d,c,e){var b={checkbox:false,checkboxChain:true,checkboxBreak:true,checkboxClick:false};var a={_checkboxInit:function(){this._instance.jQuery.bind("acitree"+this._private.nameSpace,function(j,h,i,f,g){switch(f){case"loaded":h._checkboxLoad(i);break}}).bind("keydown"+this._private.nameSpace,this.proxy(function(g){switch(g.which){case 32:if(this.extSelectable&&this.extSelectable()&&!g.ctrlKey){var f=this.focused();if(this.hasCheckbox(f)&&this.isEnabled(f)){if(this.isChecked(f)){this.uncheck(f)}else{this.check(f)}g.stopImmediatePropagation();g.preventDefault()}}break}})).on("click"+this._private.nameSpace,".aciTreeItem",this.proxy(function(g){if(!this._instance.options.checkboxClick||d(g.target).is(".aciTreeCheck")){var f=this.itemFrom(g.target);if(this.hasCheckbox(f)&&this.isEnabled(f)&&(!this.extSelectable||!this.extSelectable()||(!g.ctrlKey&&!g.shiftKey))){if(this.isChecked(f)){this.uncheck(f)}else{this.check(f)}g.preventDefault()}}}))},_initHook:function(){if(this.extCheckbox()){this._checkboxInit()}this._super()},_itemHook:function(h,i,f,j){if(this.extCheckbox()){var g=this.extRadio&&this.hasRadio(i);if(!g&&(f.checkbox||((f.checkbox===e)&&(!this.extRadio||!this.extRadio())))){this._checkboxDOM.add(i,f)}}this._super(h,i,f,j)},_checkboxDOM:{add:function(g,f){g.attr("aria-checked",!!f.checked).addClass("aciTreeCheckbox"+(f.checked?" aciTreeChecked":"")).children(".aciTreeLine").find(".aciTreeText").wrap("<label></label>").before('<span class="aciTreeCheck" />')},remove:function(g){var f=g.removeAttr("aria-checked").removeClass("aciTreeCheckbox aciTreeChecked aciTreeTristate").children(".aciTreeLine").find("label");if(f.length){f.find("*").not(".aciTreeText").remove();f.find(".aciTreeText").unwrap()}},check:function(f,g){f.attr("aria-checked",g).toggleClass("aciTreeChecked",g)},tristate:function(f,g){f.toggleClass("aciTreeTristate",g)}},_checkboxLoad:function(f){if(this._instance.options.checkboxChain===false){return}var g=e;if(this.hasCheckbox(f)){if(this.isChecked(f)){if(!this.checkboxes(this.children(f,false,true),true).length){g=true}}else{g=false}}this._checkboxUpdate(f,g)},_checkboxChildren:function(g){if(this._instance.options.checkboxBreak){var h=[];var i=this.proxy(function(k){var j=this.children(k,false,true);j.each(this.proxy(function(l){var m=d(l);if(this.hasCheckbox(m)){h.push(l);i(m)}},true))});i(g);return d(h)}else{var f=this.children(g,true,true);return this.checkboxes(f)}},_checkboxUpdate:function(i,j){var f=this.proxy(function(o,n,p){var k=this.children(o,false,true);var m=0;var l=0;k.each(this.proxy(function(r){var t=d(r);var s={total:0,checked:0};if(this.hasCheckbox(t)){if((p!==e)&&(this._instance.options.checkboxChain!==-1)){this._checkboxDOM.check(t,p)}m++;if(this.isChecked(t)){l++}f(t,s,p)}else{if(this._instance.options.checkboxBreak){var q={total:0,checked:0};f(t,q)}else{f(t,s,p)}}m+=s.total;l+=s.checked},true));if(o){this._checkboxDOM.tristate(o,(l>0)&&(l!=m));n.total+=m;n.checked+=l}});var h={total:0,checked:0};f(i,h,j);var g=this.proxy(function(o,n,p){var l=this.parent(o);if(l.length){if(!n){var k=this._checkboxChildren(l);var m=this.checkboxes(k,true).length;var n=(m>0)&&(m!=k.length)}if(this.hasCheckbox(l)){if((p!==e)&&(this._instance.options.checkboxChain!==1)){this._checkboxDOM.check(l,n?true:p)}this._checkboxDOM.tristate(l,n);g(l,n,p)}else{if(this._instance.options.checkboxBreak){g(l)}else{g(l,n,p)}}}});g(i,e,j)},hasCheckbox:function(f){return f&&f.hasClass("aciTreeCheckbox")},addCheckbox:function(g,f){f=this._options(f,"checkboxadded","addcheckboxfail","wascheckbox",g);if(this.isItem(g)){if(!this._trigger(g,"beforeaddcheckbox",f)){this._fail(g,f);return}if(this.hasCheckbox(g)){this._notify(g,f)}else{var h=function(){this._checkboxDOM.add(g,{});this._success(g,f)};if(this.extRadio&&this.hasRadio(g)){this.removeRadio(g,this._inner(f,{success:h,fail:f.fail}))}else{h.apply(this)}}}else{this._fail(g,f)}},removeCheckbox:function(g,f){f=this._options(f,"checkboxremoved","removecheckboxfail","notcheckbox",g);if(this.isItem(g)){if(!this._trigger(g,"beforeremovecheckbox",f)){this._fail(g,f);return}if(this.hasCheckbox(g)){this._checkboxDOM.remove(g);this._success(g,f)}else{this._notify(g,f)}}else{this._fail(g,f)}},isChecked:function(f){if(this.hasCheckbox(f)){return f.hasClass("aciTreeChecked")}if(this._super){return this._super(f)}return false},check:function(g,f){if(this.extCheckbox&&this.hasCheckbox(g)){f=this._options(f,"checked","checkfail","waschecked",g);if(!this._trigger(g,"beforecheck",f)){this._fail(g,f);return}if(this.isChecked(g)){this._notify(g,f)}else{this._checkboxDOM.check(g,true);if(this._instance.options.checkboxChain!==false){this._checkboxUpdate(g,true)}this._success(g,f)}}else{if(this._super){this._super(g,f)}else{this._trigger(g,"checkfail",f);this._fail(g,f)}}},uncheck:function(g,f){if(this.extCheckbox&&this.hasCheckbox(g)){f=this._options(f,"unchecked","uncheckfail","notchecked",g);if(!this._trigger(g,"beforeuncheck",f)){this._fail(g,f);return}if(this.isChecked(g)){this._checkboxDOM.check(g,false);if(this._instance.options.checkboxChain!==false){this._checkboxUpdate(g,false)}this._success(g,f)}else{this._notify(g,f)}}else{if(this._super){this._super(g,f)}else{this._trigger(g,"uncheckfail",f);this._fail(g,f)}}},checkboxes:function(f,h){var g=f.filter(".aciTreeCheckbox");if(h!==e){return h?g.filter(".aciTreeChecked"):g.not(".aciTreeChecked")}return g},_serialize:function(f,h){var g=this._super(f,h);if(g&&this.extCheckbox()){if(g.hasOwnProperty("checkbox")){g.checkbox=this.hasCheckbox(f);g.checked=this.isChecked(f)}else{if(this.hasCheckbox(f)){if(this.extRadio&&this.extRadio()){g.checkbox=true}g.checked=this.isChecked(f)}}}return g},serialize:function(g,i,j){if(i=="checkbox"){var h="";var f=this.children(g,true,true);this.checkboxes(f,true).each(this.proxy(function(k){var l=d(k);if(j){h+=j.call(this,l,i,this.getId(l))}else{h+=this._instance.options.serialize.call(this,l,i,this.getId(l))}},true));return h}return this._super(g,i,j)},isTristate:function(f){return f&&f.hasClass("aciTreeTristate")},tristate:function(f){return f.filter(".aciTreeTristate")},extCheckbox:function(){return this._instance.options.checkbox},option:function(f,g){if(this.wasInit()&&!this.isLocked()){if((f=="checkbox")&&(g!=this.extCheckbox())){if(g){this._checkboxInit()}else{this._checkboxDone()}}}this._super(f,g)},_checkboxDone:function(f){this._instance.jQuery.unbind(this._private.nameSpace);this._instance.jQuery.off(this._private.nameSpace,".aciTreeItem");if(!f){this.checkboxes(this.children(null,true,true)).each(this.proxy(function(g){this.removeCheckbox(d(g))},true))}},_destroyHook:function(f){if(f){this._checkboxDone(true)}this._super(f)}};aciPluginClass.plugins.aciTree=aciPluginClass.plugins.aciTree.extend(a,"aciTreeCheckbox");aciPluginClass.defaults("aciTree",b)})(jQuery,this);

// radio (works best with selectable)
(function(d,b,e){var a={radio:false,radioChain:true,radioBreak:true,radioClick:false};var c={_radioInit:function(){this._instance.jQuery.bind("acitree"+this._private.nameSpace,function(j,h,i,f,g){switch(f){case"loaded":if(i){h._radioLoad(i)}break}}).bind("keydown"+this._private.nameSpace,this.proxy(function(g){switch(g.which){case 32:if(this.extSelectable&&this.extSelectable()&&!g.ctrlKey){var f=this.focused();if(this.hasRadio(f)&&this.isEnabled(f)){if(!this.isChecked(f)){this.check(f)}g.stopImmediatePropagation();g.preventDefault()}}break}})).on("click"+this._private.nameSpace,".aciTreeItem",this.proxy(function(g){if(!this._instance.options.radioClick||d(g.target).is(".aciTreeCheck")){var f=this.itemFrom(g.target);if(this.hasRadio(f)&&this.isEnabled(f)&&(!this.extSelectable||!this.extSelectable()||(!g.ctrlKey&&!g.shiftKey))){if(!this.isChecked(f)){this.check(f)}g.preventDefault()}}}))},_initHook:function(){if(this.extRadio()){this._radioInit()}this._super()},_itemHook:function(g,h,f,j){if(this.extRadio()){var i=this.extCheckbox&&this.hasCheckbox(h);if(!i&&(f.radio||((f.radio===e)&&(!this.extCheckbox||!this.extCheckbox())))){this._radioDOM.add(h,f)}}this._super(g,h,f,j)},_radioDOM:{add:function(g,f){g.attr("aria-checked",!!f.checked).addClass("aciTreeRadio"+(f.checked?" aciTreeChecked":"")).children(".aciTreeLine").find(".aciTreeText").wrap("<label></label>").before('<span class="aciTreeCheck" />')},remove:function(g){var f=g.removeAttr("aria-checked").removeClass("aciTreeRadio aciTreeChecked").children(".aciTreeLine").find("label");if(f.length){f.find("*").not(".aciTreeText").remove();f.find(".aciTreeText").unwrap()}},check:function(f,g){f.attr("aria-checked",g).toggleClass("aciTreeChecked",g)}},_radioLoad:function(f){if(!this._instance.options.radioChain){return}if(this.hasRadio(f)){if(this.isChecked(f)){if(!this.radios(this.children(f,false,true),true).length){this._radioUpdate(f,true)}}else{this._radioUpdate(f)}}},_radioChildren:function(g){if(this._instance.options.radioBreak){var h=[];var i=this.proxy(function(k){var j=this.children(k,false,true);j.each(this.proxy(function(l){var m=d(l);if(this.hasRadio(m)){h.push(l);i(m)}},true))});i(g);return d(h)}else{var f=this.children(g,true,true);return this.radios(f)}},_radioLevel:function(f){var g=[];f.each(this.proxy(function(i){var j=d(i);var h=this.children(j,false,true);h.each(this.proxy(function(k){var l=d(k);if(!this._instance.options.radioBreak||this.hasRadio(l)){g.push(k)}},true))},true));return d(g)},_radioUpdate:function(h,i){var j=this.proxy(function(k){var l=this.siblings(k,true);this._radioDOM.check(this.radios(l),false);l.each(this.proxy(function(m){var n=d(m);if(!this._instance.options.radioBreak||this.hasRadio(n)){this._radioDOM.check(this._radioChildren(n),false)}},true))});if(i){j(h)}var f=this.proxy(function(m){var k=this._radioLevel(m);var n=this.radios(k);if(n.length){var l=this.radios(k,true);if(l.length){l=l.first();this._radioDOM.check(l,true);j(l);f(l)}else{l=n.first();this._radioDOM.check(l,true);j(l);f(l)}}else{if(k.length){f(k)}}});if(i){f(h)}else{this._radioDOM.check(this._radioChildren(h),false)}var g=this.proxy(function(l){var k=this.parent(l);if(k.length){if(this.hasRadio(k)){if(i){j(k)}this._radioDOM.check(k,i);g(k)}else{if(!this._instance.options.radioBreak){if(i){j(k)}g(k)}}}});if(i!==e){g(h)}},hasRadio:function(f){return f&&f.hasClass("aciTreeRadio")},addRadio:function(g,f){f=this._options(f,"radioadded","addradiofail","wasradio",g);if(this.isItem(g)){if(!this._trigger(g,"beforeaddradio",f)){this._fail(g,f);return}if(this.hasRadio(g)){this._notify(g,f)}else{var h=function(){this._radioDOM.add(g,{});this._success(g,f)};if(this.extCheckbox&&this.hasCheckbox(g)){this.removeCheckbox(g,this._inner(f,{success:h,fail:f.fail}))}else{h.apply(this)}}}else{this._fail(g,f)}},removeRadio:function(g,f){f=this._options(f,"radioremoved","removeradiofail","notradio",g);if(this.isItem(g)){if(!this._trigger(g,"beforeremoveradio",f)){this._fail(g,f);return}if(this.hasRadio(g)){this._radioDOM.remove(g);this._success(g,f)}else{this._notify(g,f)}}else{this._fail(g,f)}},isChecked:function(f){if(this.hasRadio(f)){return f.hasClass("aciTreeChecked")}if(this._super){return this._super(f)}return false},check:function(g,f){if(this.extRadio&&this.hasRadio(g)){f=this._options(f,"checked","checkfail","waschecked",g);if(!this._trigger(g,"beforecheck",f)){this._fail(g,f);return}if(this.isChecked(g)){this._notify(g,f)}else{this._radioDOM.check(g,true);if(this._instance.options.radioChain){this._radioUpdate(g,true)}this._success(g,f)}}else{if(this._super){this._super(g,f)}else{this._trigger(g,"checkfail",f);this._fail(g,f)}}},uncheck:function(g,f){if(this.extRadio&&this.hasRadio(g)){f=this._options(f,"unchecked","uncheckfail","notchecked",g);if(!this._trigger(g,"beforeuncheck",f)){this._fail(g,f);return}if(this.isChecked(g)){this._radioDOM.check(g,false);if(this._instance.options.radioChain){this._radioUpdate(g,false)}this._success(g,f)}else{this._notify(g,f)}}else{if(this._super){this._super(g,f)}else{this._trigger(g,"uncheckfail",f);this._fail(g,f)}}},radios:function(f,h){var g=f.filter(".aciTreeRadio");if(h!==e){return h?g.filter(".aciTreeChecked"):g.not(".aciTreeChecked")}return g},_serialize:function(f,h){var g=this._super(f,h);if(g&&this.extRadio()){if(g.hasOwnProperty("radio")){g.radio=this.hasRadio(f);g.checked=this.isChecked(f)}else{if(this.hasRadio(f)){if(this.extCheckbox&&this.extCheckbox()){g.radio=true}g.checked=this.isChecked(f)}}}return g},serialize:function(g,i,j){if(i=="radio"){var h="";var f=this.children(g,true,true);this.radios(f,true).each(this.proxy(function(k){var l=d(k);if(j){h+=j.call(this,l,i,this.getId(l))}else{h+=this._instance.options.serialize.call(this,l,i,this.getId(l))}},true));return h}return this._super(g,i,j)},extRadio:function(){return this._instance.options.radio},option:function(f,g){if(this.wasInit()&&!this.isLocked()){if((f=="radio")&&(g!=this.extRadio())){if(g){this._radioInit()}else{this._radioDone()}}}this._super(f,g)},_radioDone:function(f){this._instance.jQuery.unbind(this._private.nameSpace);this._instance.jQuery.off(this._private.nameSpace,".aciTreeItem");if(!f){this.radios(this.children(null,true,true)).each(this.proxy(function(g){this.removeRadio(d(g))},true))}},_destroyHook:function(f){if(f){this._radioDone(true)}this._super(f)}};aciPluginClass.plugins.aciTree=aciPluginClass.plugins.aciTree.extend(c,"aciTreeRadio");aciPluginClass.defaults("aciTree",a)})(jQuery,this);

// column
(function(d,b,e){var a={columnData:[]};var c={__extend:function(){d.extend(this._private,{propsIndex:{}});this._super()},_initHook:function(){if(this._instance.options.columnData.length){var h=false,g;for(var f in this._instance.options.columnData){g=this._instance.options.columnData[f];if(g.width!==e){this._updateCss(".aciTree.aciTree"+this._instance.index+" .aciTreeColumn"+f,"width:"+g.width+"px;");h=true}this._private.propsIndex[g.props]=f}if(h){this._updateWidth()}}this._super()},_getCss:function(l,o,n){var g="_getCss_"+b.String(l).replace(/[^a-z0-9_-]/ig,"_");var k=d("body").find("#"+g);if(!k.length){if(l instanceof Array){var f="",h="";for(var j in l){f+='<div class="'+l[j]+'">';h+="</div>"}f+=h}else{var f='<div class="'+l+'"></div>'}d("body").append('<div id="'+g+'" style="position:relative;display:inline-block;width:0px;height:0px;line-height:0px;overflow:hidden">'+f+"</div>");k=d("body").find("#"+g)}var m=k.find("*:last").css(o);if(n){m=parseInt(m);if(isNaN(m)){m=null}}return m},_updateCss:function(h,f){var j="_updateCss_"+b.String(h).replace(">","_gt_").replace(/[^a-z0-9_-]/ig,"_");var g='<style id="'+j+'" type="text/css">'+h+"{"+f+"}</style>";var i=d("body").find("#"+j);if(i.length){i.replaceWith(g)}else{d("body").prepend(g)}},getWidth:function(f){if((f>=0)&&(f<this.columns())){return this._getCss(["aciTree aciTree"+this._instance.index,"aciTreeColumn"+f],"width",true)}return null},setWidth:function(f,g){if((f>=0)&&(f<this.columns())){this._updateCss(".aciTree.aciTree"+this._instance.index+" .aciTreeColumn"+f,"width:"+g+"px;");this._updateWidth()}},_updateWidth:function(){var h=0;for(var f in this._instance.options.columnData){if(this.isColumn(f)){h+=this.getWidth(f)}}var g=this._getCss(["aciTree","aciTreeIcon"],"width",true);h+=this._getCss(["aciTree","aciTreeItem"],"padding-left",true)+this._getCss(["aciTree","aciTreeItem"],"padding-right",true);this._updateCss(".aciTree.aciTree"+this._instance.index+" .aciTreeItem","margin-right:"+(g+h)+"px;");this._updateCss(".aciTree[dir=rtl].aciTree"+this._instance.index+" .aciTreeItem","margin-right:0;margin-left:"+(g+h)+"px;")},isColumn:function(f){if((f>=0)&&(f<this.columns())){return this._getCss(["aciTree aciTree"+this._instance.index,"aciTreeColumn"+f],"display")!="none"}return false},columnIndex:function(f){if(this._private.propsIndex[f]!==e){return this._private.propsIndex[f]}return -1},columns:function(){return this._instance.options.columnData.length},toggleColumn:function(g,f){if((g>=0)&&(g<this.columns())){if(f===e){var f=!this.isColumn(g)}this._updateCss(".aciTree.aciTree"+this._instance.index+" .aciTreeColumn"+g,"display:"+(f?"inherit":"none")+";");this._updateWidth()}},_itemHook:function(k,l,g,n){if(this.columns()){var f=l.children(".aciTreeLine").find(".aciTreeEntry");var m,j;for(var h in this._instance.options.columnData){m=this._instance.options.columnData[h];j=this._createColumn(g,m,h);f.prepend(j)}}this._super(k,l,g,n)},_createColumn:function(f,h,g){var i=h.props&&(f[h.props]!==e)?f[h.props]:((h.value===e)?"":h.value);return d('<div class="aciTreeColumn aciTreeColumn'+g+'">'+(i.length?i:"&nbsp;")+"</div>")},setColumn:function(g,f){f=this._options(f,"columnset","columnfail","wascolumn",g);if(this.isItem(g)&&(f.index>=0)&&(f.index<this.columns())){if(!this._trigger(g,"beforecolumn",f)){this._fail(g,f);return}var h=this.itemData(g);f.oldValue=h[this._instance.options.columnData[f.index].props];if(f.value==f.oldValue){this._notify(g,f)}else{g.children(".aciTreeLine").find(".aciTreeColumn"+f.index).html(f.value);h[this._instance.options.columnData[f.index].props]=f.value;this._success(g,f)}}else{this._fail(g,f)}},getColumn:function(g,f){if((f>=0)&&(f<this.columns())){var h=this.itemData(g);return h?h[this._instance.options.columnData[f].props]:null}return null}};aciPluginClass.plugins.aciTree=aciPluginClass.plugins.aciTree.extend(c,"aciTreeColumn");aciPluginClass.defaults("aciTree",a)})(jQuery,this);

// editable (works best with selectable)
(function(c,b,e){var a={editable:false,editDelay:250};var d={__extend:function(){c.extend(this._private,{editTimestamp:null});this._super()},_editableInit:function(){this._instance.jQuery.bind("acitree"+this._private.nameSpace,function(j,h,i,f,g){switch(f){case"blurred":var i=h.edited();if(i.length){h.endEdit()}break;case"deselected":if(h.isEdited(i)){h.endEdit()}break}}).bind("click"+this._private.nameSpace,this.proxy(function(){var f=this.edited();if(f.length){this.endEdit()}})).bind("keydown"+this._private.nameSpace,this.proxy(function(g){switch(g.which){case 113:if(this.extSelectable&&this.extSelectable()){var f=this.focused();if(f.length&&!this.isEdited(f)&&this.isEnabled(f)){this.edit(f);g.preventDefault()}}break}})).on("mousedown"+this._private.nameSpace,".aciTreeItem",this.proxy(function(f){if(c(f.target).is(".aciTreeItem,.aciTreeText")){this._private.editTimestamp=c.now()}})).on("mouseup"+this._private.nameSpace,".aciTreeItem",this.proxy(function(g){if(c(g.target).is(".aciTreeItem,.aciTreeText")){var h=c.now()-this._private.editTimestamp;if((h>this._instance.options.editDelay)&&(h<this._instance.options.editDelay*4)){var f=this.itemFrom(g.target);if((!this.extSelectable||!this.extSelectable()||(this.isFocused(f)&&(this.selected().length==1)))&&this.isEnabled(f)){this.edit(f)}}}})).on("keydown"+this._private.nameSpace,"input[type=text]",this.proxy(function(f){switch(f.which){case 13:this.itemFrom(f.target).focus();this.endEdit();f.stopPropagation();break;case 27:this.itemFrom(f.target).focus();this.endEdit({save:false});f.stopPropagation();f.preventDefault();break;case 38:case 40:case 37:case 39:case 33:case 34:case 36:case 35:case 32:case 107:case 109:case 106:f.stopPropagation();break}})).on("blur"+this._private.nameSpace,"input[type=text]",this.proxy(function(){if(!this.extSelectable||!this.extSelectable()){this.endEdit()}})).on("click"+this._private.nameSpace+" dblclick"+this._private.nameSpace,"input[type=text]",function(f){f.stopPropagation()})},_initHook:function(){if(this.extEditable()){this._editableInit()}this._super()},_editableDOM:{add:function(g){var f=g.addClass("aciTreeEdited").children(".aciTreeLine");f.find(".aciTreeText").html('<input id="aciTree-editable-tree-item" type="text" value="" style="-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;-o-user-select:text;user-select:text" />');f.find("label").attr("for","aciTree-editable-tree-item");this._editableDOM.get(g).val(this.getLabel(g))},remove:function(h,g){var f=h.removeClass("aciTreeEdited").children(".aciTreeLine");f.find(".aciTreeText").html(this.getLabel(h));f.find("label").removeAttr("for")},get:function(f){return f?f.children(".aciTreeLine").find("input[type=text]"):c([])}},edited:function(){return this._instance.jQuery.find(".aciTreeEdited")},isEdited:function(f){return f&&f.hasClass("aciTreeEdited")},_focusEdit:function(g){var h=this._editableDOM.get(g).focus().trigger("click").get(0);if(h){if(typeof h.selectionStart=="number"){h.selectionStart=h.selectionEnd=h.value.length}else{if(h.createTextRange!==e){var f=h.createTextRange();f.collapse(false);f.select()}}}},setLabel:function(g,f){if(!this.extEditable()||!this.isEdited(g)){this._super(g,f)}},edit:function(g,f){f=this._options(f,"edit","editfail","wasedit",g);if(this.extEditable()&&this.isItem(g)){if(!this._trigger(g,"beforeedit",f)){this._fail(g,f);return}var h=this.edited();if(h.length){if(h.get(0)==g.get(0)){this._notify(g,f);return}else{this._editableDOM.remove.call(this,h);this._trigger(h,"endedit",f)}}this._editableDOM.add.call(this,g);this._focusEdit(g);this._success(g,f)}else{this._fail(g,f)}},endEdit:function(f){var g=this.edited();f=this._options(f,"edited","endeditfail","endedit",g);if(this.extEditable()&&this.isItem(g)){if(!this._trigger(g,"beforeendedit",f)){this._fail(g,f);return}var h=this._editableDOM.get(g).val();this._editableDOM.remove.call(this,g);if((f.save===e)||f.save){this.setLabel(g,{label:h});this._success(g,f)}else{this._notify(g,f)}}else{this._fail(g,f)}},extEditable:function(){return this._instance.options.editable},option:function(f,g){if(this.wasInit()&&!this.isLocked()){if((f=="editable")&&(g!=this.extEditable())){if(g){this._editableInit()}else{this._editableDone()}}}this._super(f,g)},_editableDone:function(){this._instance.jQuery.unbind(this._private.nameSpace);this._instance.jQuery.off(this._private.nameSpace,".aciTreeItem");this._instance.jQuery.off(this._private.nameSpace,"input[type=text]");var f=this.edited();if(f.length){this.endEdit()}},_destroyHook:function(f){if(f){this._editableDone()}this._super(f)}};aciPluginClass.plugins.aciTree=aciPluginClass.plugins.aciTree.extend(d,"aciTreeEditable");aciPluginClass.defaults("aciTree",a)})(jQuery,this);

// persist (require utils extension & jStorage plugin)
(function(c,b,e){var a={persist:null};var d={__extend:function(){c.extend(this._private,{selectTimeout:null,focusTimeout:null,openTimeout:null});this._super()},_initPersist:function(){this._instance.jQuery.bind("acitree"+this._private.nameSpace,function(j,h,i,f,g){if(g.uid=="ui.persist"){return}switch(f){case"init":h._persistRestore();break;case"selected":case"deselected":h._persistLater("selected");break;case"focus":case"blur":h._persistLater("focused");break;case"opened":case"closed":h._persistLater("opened");break}})},_initHook:function(){if(this.extPersist()){this._initPersist()}this._super()},_persistLater:function(f){switch(f){case"selected":b.clearTimeout(this._private.selectTimeout);this._private.selectTimeout=b.setTimeout(this.proxy(function(){this._persistSelected()}),250);break;case"focused":b.clearTimeout(this._private.focusTimeout);this._private.focusTimeout=b.setTimeout(this.proxy(function(){this._persistFocused()}),250);break;case"opened":b.clearTimeout(this._private.openTimeout);this._private.openTimeout=b.setTimeout(this.proxy(function(){this._persistOpened()}),250);break}},_persistRestoreX:function(){var f=new this._queue(this,this._instance.options.queue);var k=c.jStorage.get("aciTree_"+this._instance.options.persist+"_opened");if(k instanceof Array){for(var g in k){(function(i){f.push(function(l){this.searchPath(null,{success:function(m){this.open(m,{uid:"ui.persist",success:l,fail:l})},fail:l,path:i.split(";"),load:true})})})(k[g])}}if(this.extSelectable&&this.extSelectable()){var h=c.jStorage.get("aciTree_"+this._instance.options.persist+"_selected");if(h instanceof Array){for(var g in h){(function(i){f.push(function(l){this.searchPath(null,{success:function(m){this.select(m,{uid:"ui.persist",success:function(){l()},fail:l,focus:false})},fail:l,path:i.split(";")})})})(h[g]);if(!this._instance.options.multiSelectable){break}}}var j=c.jStorage.get("aciTree_"+this._instance.options.persist+"_focused");if(j instanceof Array){for(var g in j){(function(i){f.push(function(l){this.searchPath(null,{success:function(m){this.focus(m,{uid:"ui.persist",success:function(n){this.setVisible(n,{center:true});l()},fail:l})},fail:l,path:i.split(";")})})})(j[g])}}}},_persistRestore:function(){var f=new this._queue(this,this._instance.options.queue);var g=new this._task(f,function(k){if(this.extSelectable&&this.extSelectable()){var m=c.jStorage.get("aciTree_"+this._instance.options.persist+"_selected");if(m instanceof Array){for(var l in m){(function(i){f.push(function(o){this.searchPath(null,{success:function(p){this.select(p,{uid:"ui.persist",success:function(){o()},fail:o,focus:false})},fail:o,path:i.split(";")})})})(m[l]);if(!this._instance.options.multiSelectable){break}}}var n=c.jStorage.get("aciTree_"+this._instance.options.persist+"_focused");if(n instanceof Array){for(var l in n){(function(i){f.push(function(o){this.searchPath(null,{success:function(p){this.focus(p,{uid:"ui.persist",success:function(q){this.setVisible(q,{center:true});o()},fail:o})},fail:o,path:i.split(";")})})})(n[l])}}}k()});var j=c.jStorage.get("aciTree_"+this._instance.options.persist+"_opened");if(j instanceof Array){for(var h in j){(function(i){g.push(function(k){this.searchPath(null,{success:function(l){this.open(l,{uid:"ui.persist",success:k,fail:k})},fail:k,path:i.split(";"),load:true})})})(j[h])}}},_persistSelected:function(){if(this.extSelectable&&this.extSelectable()){var f=[];this.selected().each(this.proxy(function(g){var h=c(g);var i=this.pathId(h);i.push(this.getId(h));f.push(i.join(";"))},true));c.jStorage.set("aciTree_"+this._instance.options.persist+"_selected",f)}},_persistFocused:function(){if(this.extSelectable&&this.extSelectable()){var f=[];this.focused().each(this.proxy(function(g){var h=c(g);var i=this.pathId(h);i.push(this.getId(h));f.push(i.join(";"))},true));c.jStorage.set("aciTree_"+this._instance.options.persist+"_focused",f)}},_persistOpened:function(){var f=[];this.inodes(this.children(null,true),true).each(this.proxy(function(g){var h=c(g);if(this.isOpenPath(h)){var i=this.pathId(h);i.push(this.getId(h));f.push(i.join(";"))}},true));c.jStorage.set("aciTree_"+this._instance.options.persist+"_opened",f)},isPersist:function(){if(this.extPersist()){var f=c.jStorage.get("aciTree_"+this._instance.options.persist+"_selected");if(f instanceof Array){return true}var g=c.jStorage.get("aciTree_"+this._instance.options.persist+"_focused");if(g instanceof Array){return true}var h=c.jStorage.get("aciTree_"+this._instance.options.persist+"_opened");if(h instanceof Array){return true}}return false},unpersist:function(){if(this.extPersist()){c.jStorage.deleteKey("aciTree_"+this._instance.options.persist+"_selected");c.jStorage.deleteKey("aciTree_"+this._instance.options.persist+"_focused");c.jStorage.deleteKey("aciTree_"+this._instance.options.persist+"_opened")}},extPersist:function(){return this._instance.options.persist},option:function(f,h){var g=this.extPersist();this._super(f,h);if(this.extPersist()!=g){if(g){this._donePersist()}else{this._initPersist()}}},_donePersist:function(){this._instance.jQuery.unbind(this._private.nameSpace)},_destroyHook:function(f){if(f){this._donePersist()}this._super(f)}};aciPluginClass.plugins.aciTree=aciPluginClass.plugins.aciTree.extend(d,"aciTreePersist");aciPluginClass.defaults("aciTree",a)})(jQuery,this);

// hash (require utils extension & aciFragment plugin)
(function(d,c,e){var a={selectHash:null,openHash:null};var b={__extend:function(){d.extend(this._private,{lastSelect:null,lastOpen:null,hashApi:null});this._super()},_hashInit:function(){this._instance.jQuery.aciFragment();this._private.hashApi=this._instance.jQuery.aciFragment("api");this._instance.jQuery.bind("acitree"+this._private.nameSpace,function(j,h,i,f,g){switch(f){case"init":h._hashRestore();break}}).bind("acifragment"+this._private.nameSpace,this.proxy(function(h,g,f){h.stopPropagation();this._hashRestore()}))},_initHook:function(){if(this.extHast()){this._hashInit()}this._super()},_hashRestore:function(){var f=this._instance.queue;var i=function(l){for(var k in l){(function(m){f.push(function(n){this.search(null,{success:function(o){this.open(o,{uid:"ui.hash",success:n,fail:n})},fail:n,search:m})})})(l[k])}};if(this._instance.options.openHash){var h=this._private.hashApi.get(this._instance.options.openHash,"");if(h.length&&(h!=this._private.lastOpen)){this._private.lastOpen=h;var j=h.split(";");i(j)}}if(this._instance.options.selectHash&&this.extSelectable&&this.extSelectable()){var h=this._private.hashApi.get(this._instance.options.selectHash,"");if(h.length&&(h!=this._private.lastSelect)){this._private.lastSelect=h;var j=h.split(";");var g=j.pop();i(j);if(g){f.push(function(k){this.search(null,{success:function(l){this.select(l,{uid:"ui.hash",success:function(m){this.setVisible(m,{center:true});k()},fail:k})},fail:k,search:g})})}}}},extHast:function(){return this._instance.options.selectHash||this._instance.options.openHash},option:function(f,g){var h=this.extHast();this._super(f,g);if(this.extHast()!=h){if(h){this._hashDone()}else{this._hashInit()}}},_hashDone:function(){this._instance.jQuery.unbind(this._private.nameSpace);this._private.hashApi=null;this._instance.jQuery.aciFragment("destroy")},_destroyHook:function(f){if(f){this._hashDone()}this._super(f)}};aciPluginClass.plugins.aciTree=aciPluginClass.plugins.aciTree.extend(b,"aciTreeHash");aciPluginClass.defaults("aciTree",a)})(jQuery,this);

// sortable (require utils extension & aciSortable plugin)
(function(d,c,e){var a={sortable:false,sortDelay:750,sortDrag:function(g,i,h,f){if(!h){f.html(this.getLabel(g))}},sortValid:function(i,g,j,f,k,h){if(f){h.html("move "+this.getLabel(i)+" to "+this.getLabel(this.itemFrom(g)));k.removeClass("aciTreeAfter aciTreeBefore")}else{if(j!==null){if(j){h.html("move "+this.getLabel(i)+" before "+this.getLabel(g));k.removeClass("aciTreeAfter").addClass("aciTreeBefore")}else{h.html("move "+this.getLabel(i)+" after "+this.getLabel(g));k.removeClass("aciTreeBefore").addClass("aciTreeAfter")}}}}};var b={__extend:function(){d.extend(this._private,{openTimeout:null,dragDrop:null});this._super()},_sortableInit:function(){this._instance.jQuery.aciSortable({container:".aciTreeUl",item:".aciTreeLi",child:50,childHolder:'<ul class="aciTreeUl aciTreeChild"></ul>',childHolderSelector:".aciTreeChild",placeholder:'<li class="aciTreeLi aciTreePlaceholder"><div></div></li>',placeholderSelector:".aciTreePlaceholder",helper:'<div class="aciTreeHelper"></div>',helperSelector:".aciTreeHelper",before:this.proxy(function(f){if(!this._initDrag(f)){return false}if(!this._trigger(f,"beforedrag")){this._trigger(f,"dragfail");return false}return true}),start:this.proxy(function(g,h,f){this._instance.jQuery.addClass("aciTreeDragDrop");f.css({opacity:1}).html(this.getLabel(g))}),drag:this.proxy(function(g,i,h,f){if(!h){c.clearTimeout(this._private.openTimeout)}if(this._instance.options.sortDrag){this._instance.options.sortDrag.apply(this,arguments)}}),valid:this.proxy(function(j,h,k,g,l,i){c.clearTimeout(this._private.openTimeout);if(!this._checkDrop(j,h,k,g,l,i)){return false}var f=this._options({hover:h,before:k,isContainer:g,placeholder:l,helper:i});if(!this._trigger(j,"checkdrop",f)){return false}if(!g&&this.isInode(h)){if(!this.isOpen(h)&&!h.data("opening"+this._private.nameSpace)){this._private.openTimeout=c.setTimeout(this.proxy(function(){h.data("opening"+this._private.nameSpace,true);this.open(h,{success:function(m){m.removeData("opening"+this._private.nameSpace)},fail:function(m){m.removeData("opening"+this._private.nameSpace)}})}),this._instance.options.sortDelay)}}if(this._instance.options.sortValid){this._instance.options.sortValid.apply(this,arguments)}return true}),create:this.proxy(function(g,h,f){if(this.isLeaf(f)){f.append(g._instance.options.childHolder);return true}return false},true),end:this.proxy(function(q,k,p,g){c.clearTimeout(this._private.openTimeout);var r={placeholder:p,helper:g};r=this._options(r,"sorted","dropfail",null,q);if(p.parent().length){var i=this.prev(p,true);if(i.length){p.detach();var l=d(this._private.dragDrop.get().reverse());this._private.dragDrop=null;l.each(this.proxy(function(s){this.moveAfter(d(s),this._inner(r,{success:r.success,fail:r.fail,after:i}))},true))}else{var j=this.next(p,true);if(j.length){p.detach();var l=d(this._private.dragDrop.get().reverse());this._private.dragDrop=null;l.each(this.proxy(function(s){this.moveBefore(d(s),this._inner(r,{success:r.success,fail:r.fail,before:j}))},true))}else{var o=this.parent(p);var f=p.parent();p.detach();f.remove();if(this.isLeaf(o)){var l=this._private.dragDrop;this.asChild(l.eq(0),this._inner(r,{success:function(){this._success(q,r);this.open(o);l.filter(":gt(0)").each(this.proxy(function(s){this.moveAfter(d(s),this._inner(r,{success:r.success,fail:r.fail,after:this.last(o)}))},true))},fail:r.fail,parent:o}))}else{this._fail(q,r)}}}}else{this._fail(q,r)}this._private.dragDrop=null;if(g.parent().length){var n=d(c).scrollTop();var h=d(c).scrollLeft();var m=q.get(0).getBoundingClientRect();g.animate({top:m.top+n,left:m.left+h,opacity:0},{complete:function(){g.detach()}})}this._instance.jQuery.removeClass("aciTreeDragDrop")})})},_initHook:function(){if(this.extSortable()){this._sortableInit()}this._super()},_parents:function(l){var h=l.length,k,g,f=[];for(var n=0;n<h-1;n++){k=l.eq(n);for(var m=n+1;m<h;m++){g=l.eq(m);if(this.isChildren(k,g)){f.push(l[m])}else{if(this.isChildren(g,k)){f.push(l[n])}}}}return l.not(f)},_initDrag:function(g){if(this.extSelectable&&this.extSelectable()){if(!this.hasFocus()){this._instance.jQuery.focus()}if(!this.isEnabled(g)){return false}var f=this.selected();if(f.length){if(!this.isSelected(g)){return false}}else{f=g}this._private.dragDrop=this._parents(f)}else{this._instance.jQuery.focus();this._private.dragDrop=g}return true},_checkDrop:function(j,h,k,g,l,i){var f=this._private.dragDrop;if(!f){return false}var m=this.itemFrom(h);if(f.is(m)||f.has(m.get(0)).length){return false}if(!g){m=k?this.prev(h):this.next(h);if(f.is(m)){return false}}return true},extSortable:function(){return this._instance.options.sortable},option:function(f,g){if(this.wasInit()&&!this.isLocked()){if((f=="sortable")&&(g!=this.extSortable())){if(g){this._sortableInit()}else{this._sortableDone()}}}this._super(f,g)},_sortableDone:function(){this._instance.jQuery.unbind(this._private.nameSpace);this._instance.jQuery.aciSortable("destroy")},_destroyHook:function(f){if(f){this._sortableDone()}this._super(f)}};aciPluginClass.plugins.aciTree=aciPluginClass.plugins.aciTree.extend(b,"aciTreeSortable");aciPluginClass.defaults("aciTree",a)})(jQuery,this);