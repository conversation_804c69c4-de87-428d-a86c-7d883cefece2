/*! UIkit 2.3.1 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */

(function(t,e,n){var i=function(t,n){var s=e(t);s.data("markdownarea")||(this.element=s,this.options=e.extend({},i.defaults,n),this.init(),this.element.data("markdownarea",this))};e.extend(i.prototype,{init:function(){var t=this,s=i.template;s=s.replace(/\{\:lblPreview\}/g,this.options.lblPreview),s=s.replace(/\{\:lblCodeview\}/g,this.options.lblCodeview),this.markdownarea=e(s),this.content=this.markdownarea.find(".uk-markdownarea-content"),this.toolbar=this.markdownarea.find(".uk-markdownarea-toolbar"),this.preview=this.markdownarea.find(".uk-markdownarea-preview").children().eq(0),this.code=this.markdownarea.find(".uk-markdownarea-code"),this.element.before(this.markdownarea).appendTo(this.code),this.editor=CodeMirror.fromTextArea(this.element[0],this.options.codemirror),this.editor.markdownarea=this,this.editor.on("change",function(){var e=function(){var e=t.editor.getValue();t.originalvalue=e+"",t.currentvalue=e+"",t.element.trigger("markdownarea-before",[t]),marked(t.currentvalue,function(e,n){if(e)throw e;t.preview.html(n),t.element.val(t.currentvalue).trigger("markdownarea-update",[t])})};return e(),n.Utils.debounce(e,200)}()),this._buildtoolbar(),this.fit(),e(window).on("resize",n.Utils.debounce(function(){t.fit()},200));var a=t.preview.parent(),o=this.code.find(".CodeMirror-sizer"),r=this.code.find(".CodeMirror-scroll").on("scroll",n.Utils.debounce(function(){if("tab"!=t.markdownarea.attr("data-mode")){var e=o.height()-r.height(),n=a[0].scrollHeight-a.height(),i=n/e,s=r.scrollTop()*i;a.scrollTop(s)}},10));this.markdownarea.on("click",".uk-markdown-button-markdown, .uk-markdown-button-preview",function(n){n.preventDefault(),"tab"==t.markdownarea.attr("data-mode")&&(t.markdownarea.find(".uk-markdown-button-markdown, .uk-markdown-button-preview").removeClass("uk-active").filter(this).addClass("uk-active"),t.activetab=e(this).hasClass("uk-markdown-button-markdown")?"code":"preview",t.markdownarea.attr("data-active-tab",t.activetab))}),this.preview.parent().css("height",this.code.height())},_buildtoolbar:function(){if(this.options.toolbar&&this.options.toolbar.length){var t=this,n=[];this.options.toolbar.forEach(function(e){if(i.commands[e]){var s=i.commands[e].title?i.commands[e].title:e;n.push('<li><a data-markdownarea-cmd="'+e+'" title="'+s+'" data-uk-tooltip>'+i.commands[e].label+"</a></li>"),i.commands[e].shortcut&&t.registerShortcut(i.commands[e].shortcut,i.commands[e].action)}}),this.toolbar.html(n.join("\n")),this.markdownarea.on("click","a[data-markdownarea-cmd]",function(){var n=e(this).data("markdownareaCmd");!n||!i.commands[n]||t.activetab&&"code"!=t.activetab||i.commands[n].action.apply(t,[t.editor])})}},fit:function(){var t=this.options.mode;"split"==t&&this.markdownarea.width()<this.options.maxsplitsize&&(t="tab"),"tab"==t&&(this.activetab||(this.activetab="code",this.markdownarea.attr("data-active-tab",this.activetab)),this.markdownarea.find(".uk-markdown-button-markdown, .uk-markdown-button-preview").removeClass("uk-active").filter("code"==this.activetab?".uk-markdown-button-markdown":".uk-markdown-button-preview").addClass("uk-active")),this.markdownarea.attr("data-mode",t)},registerShortcut:function(t,n){var i=this;t=e.isArray(t)?t:[t];for(var s=0,a=t.length;a>s;s++){var o={};o[t[s]]=function(){n.apply(i,[i.editor])},i.editor.addKeyMap(o)}}}),e.fn.markdownarea=function(t){return this.each(function(){var n=e(this);n.data("markdownarea")||new i(n,t)})};var s=function(t,e){var n=e.getSelection(),i=t.replace("$1",n);e.replaceSelection(i,"end")};i.commands={fullscreen:{title:"Fullscreen",label:'<i class="uk-icon-expand"></i>',action:function(t){t.markdownarea.markdownarea.toggleClass("uk-markdownarea-fullscreen");var e=t.getWrapperElement();if(t.markdownarea.markdownarea.hasClass("uk-markdownarea-fullscreen"))t.state.fullScreenRestore={scrollTop:window.pageYOffset,scrollLeft:window.pageXOffset,width:e.style.width,height:e.style.height},e.style.width="",e.style.height=t.markdownarea.content.height()+"px",document.documentElement.style.overflow="hidden";else{document.documentElement.style.overflow="";var n=t.state.fullScreenRestore;e.style.width=n.width,e.style.height=n.height,window.scrollTo(n.scrollLeft,n.scrollTop)}t.refresh(),t.markdownarea.preview.parent().css("height",t.markdownarea.code.height())}},bold:{title:"Bold",label:'<i class="uk-icon-bold"></i>',shortcut:["Ctrl-B","Cmd-B"],action:function(t){s("**$1**",t)}},italic:{title:"Italic",label:'<i class="uk-icon-italic"></i>',action:function(t){s("*$1*",t)}},strike:{title:"Strikethrough",label:'<i class="uk-icon-strikethrough"></i>',action:function(t){s("~~$1~~",t)}},blockquote:{title:"Blockquote",label:'<i class="uk-icon-quote-right"></i>',action:function(t){s("> $1",t)}},link:{title:"Link",label:'<i class="uk-icon-link"></i>',action:function(t){s("[$1](http://)",t)}},picture:{title:"Picture",label:'<i class="uk-icon-picture-o"></i>',action:function(t){s("![$1](http://)",t)}},listUl:{title:"Unordered List",label:'<i class="uk-icon-list-ul"></i>',action:function(t){s("* $1",t)}},listOl:{title:"Ordered List",label:'<i class="uk-icon-list-ol"></i>',action:function(t){s("1. $1",t)}}},i.defaults={mode:"split",maxsplitsize:1e3,codemirror:{mode:"gfm",tabMode:"indent",tabindex:"2",lineWrapping:!0,dragDrop:!1},toolbar:["bold","italic","strike","link","picture","blockquote","listUl","listOl"],lblPreview:"Preview",lblCodeview:"Markdown"},i.template='<div class="uk-markdownarea uk-clearfix" data-mode="split"><div class="uk-markdownarea-navbar"><ul class="uk-markdownarea-navbar-nav uk-markdownarea-toolbar"></ul><div class="uk-markdownarea-navbar-flip"><ul class="uk-markdownarea-navbar-nav"><li class="uk-markdown-button-markdown"><a>{:lblCodeview}</a></li><li class="uk-markdown-button-preview"><a>{:lblPreview}</a></li><li><a data-markdownarea-cmd="fullscreen"><i class="uk-icon-expand"></i></a></li></ul></div></div><div class="uk-markdownarea-content"><div class="uk-markdownarea-code"></div><div class="uk-markdownarea-preview"><div></div></div></div></div>',n.markdownarea=i,e(function(){marked.setOptions({gfm:!0,tables:!0,breaks:!0,pedantic:!1,sanitize:!1,smartLists:!0,smartypants:!1,langPrefix:"lang-"}),e("textarea[data-uk-markdownarea]").each(function(){var t,s=e(this);s.data("markdownarea")||(t=new i(s,n.Utils.options(s.attr("data-uk-markdownarea"))))})})})(window,jQuery,jQuery.UIkit);