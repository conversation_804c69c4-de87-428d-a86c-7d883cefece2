/*! UIkit 2.3.1 | http://www.getuikit.com | (c) 2014 YOOtheme | MIT License */

/* ========================================================================
   Addon: Markdownarea
 ========================================================================== */
/* Sub-object `uk-markdownarea-navbar`
 ========================================================================== */
.uk-markdownarea-navbar {
  background: #eeeeee;
}
/*
 * Micro clearfix
 */
.uk-markdownarea-navbar:before,
.uk-markdownarea-navbar:after {
  content: " ";
  display: table;
}
.uk-markdownarea-navbar:after {
  clear: both;
}
/* Sub-object `uk-markdownarea-navbar-nav`
 ========================================================================== */
.uk-markdownarea-navbar-nav {
  margin: 0;
  padding: 0;
  list-style: none;
  float: left;
}
.uk-markdownarea-navbar-nav > li {
  float: left;
}
/*
 * 1. Dimensions
 * 2. Style
 */
.uk-markdownarea-navbar-nav > li > a {
  display: block;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  text-decoration: none;
  /* 1 */
  height: 40px;
  padding: 0 15px;
  line-height: 40px;
  /* 2 */
  color: #444444;
  font-size: 11px;
  cursor: pointer;
}
/*
 * Hover
 * 1. Apply hover style also to focus state
 * 2. Remove default focus style
 */
.uk-markdownarea-navbar-nav > li:hover > a,
.uk-markdownarea-navbar-nav > li > a:focus {
  background-color: #f5f5f5;
  color: #444444;
  outline: none;
  /* 2 */
}
/* OnClick */
.uk-markdownarea-navbar-nav > li > a:active {
  background-color: #dddddd;
  color: #444444;
}
/* Active */
.uk-markdownarea-navbar-nav > li.uk-active > a {
  background-color: #f5f5f5;
  color: #444444;
}
/* Sub-object: `uk-markdownarea-navbar-flip`
 ========================================================================== */
.uk-markdownarea-navbar-flip {
  float: right;
}
/* Sub-object for special buttons
 ========================================================================== */
[data-mode='split'] .uk-markdown-button-markdown,
[data-mode='split'] .uk-markdown-button-preview {
  display: none;
}
/* Sub-object `uk-markdownarea-content`
 ========================================================================== */
.uk-markdownarea-content {
  border-left: 1px solid #dddddd;
  border-right: 1px solid #dddddd;
  border-bottom: 1px solid #dddddd;
  background: #ffffff;
}
/*
 * Micro clearfix
 */
.uk-markdownarea-content:before,
.uk-markdownarea-content:after {
  content: " ";
  display: table;
}
.uk-markdownarea-content:after {
  clear: both;
}
/* Modifier `uk-markdownarea-fullscreen`
 ========================================================================== */
.uk-markdownarea-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1030;
}
.uk-markdownarea-fullscreen .uk-markdownarea-content {
  position: absolute;
  top: 40px;
  left: 0;
  right: 0;
  bottom: 0;
}
.uk-markdownarea-fullscreen .uk-icon-expand:before {
  content: "\f066";
}
/* Sub-objects `uk-markdownarea-code` and `uk-markdownarea-preview`
 ========================================================================== */
.uk-markdownarea-code,
.uk-markdownarea-preview {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.uk-markdownarea-preview {
  padding: 20px;
  overflow-y: scroll;
}
/*
 * Tab view
 */
[data-mode='tab'][data-active-tab='code'] .uk-markdownarea-preview,
[data-mode='tab'][data-active-tab='preview'] .uk-markdownarea-code {
  display: none;
}
/*
 * Split view
 */
[data-mode='split'] .uk-markdownarea-code,
[data-mode='split'] .uk-markdownarea-preview {
  float: left;
  width: 50%;
}
[data-mode='split'] .uk-markdownarea-code {
  border-right: 1px solid #eeeeee;
}
/* CodeMirror modifications
 ========================================================================== */
.uk-markdownarea .CodeMirror {
  padding: 10px;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
