/*! normalize.css v3.0.0 | MIT License | git.io/normalize */
html {
  font-family: sans-serif;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
}
body {
  margin: 0;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section,
summary {
  display: block;
}
audio,
canvas,
progress,
video {
  display: inline-block;
  vertical-align: baseline;
}
audio:not([controls]) {
  display: none;
  height: 0;
}
[hidden],
template {
  display: none;
}
a {
  background: transparent;
}
a:active,
a:hover {
  outline: 0;
}
abbr[title] {
  border-bottom: 1px dotted;
}
b,
strong {
  font-weight: bold;
}
dfn {
  font-style: italic;
}
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}
mark {
  background: #ff0;
  color: #000;
}
small {
  font-size: 80%;
}
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sup {
  top: -0.5em;
}
sub {
  bottom: -0.25em;
}
img {
  border: 0;
}
svg:not(:root) {
  overflow: hidden;
}
figure {
  margin: 1em 40px;
}
hr {
  -moz-box-sizing: content-box;
  box-sizing: content-box;
  height: 0;
}
pre {
  overflow: auto;
}
code,
kbd,
pre,
samp {
  font-family: monospace, monospace;
  font-size: 1em;
}
button,
input,
optgroup,
select,
textarea {
  color: inherit;
  font: inherit;
  margin: 0;
}
button {
  overflow: visible;
}
button,
select {
  text-transform: none;
}
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
}
button[disabled],
html input[disabled] {
  cursor: default;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
  padding: 0;
}
input {
  line-height: normal;
}
input[type="checkbox"],
input[type="radio"] {
  box-sizing: border-box;
  padding: 0;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  height: auto;
}
input[type="search"] {
  -webkit-appearance: textfield;
  -moz-box-sizing: content-box;
  -webkit-box-sizing: content-box;
  box-sizing: content-box;
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}
fieldset {
  border: 1px solid #c0c0c0;
  margin: 0 2px;
  padding: 0.35em 0.625em 0.75em;
}
legend {
  border: 0;
  padding: 0;
}
textarea {
  overflow: auto;
}
optgroup {
  font-weight: bold;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
}
td,
th {
  padding: 0;
}
@media print {
  * {
    text-shadow: none !important;
    color: #000 !important;
    background: transparent !important;
    box-shadow: none !important;
  }
  a,
  a:visited {
    text-decoration: underline;
  }
  a[href]:after {
    content: " (" attr(href) ")";
  }
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  a[href^="javascript:"]:after,
  a[href^="#"]:after {
    content: "";
  }
  pre,
  blockquote {
    border: 1px solid #999;
    page-break-inside: avoid;
  }
  thead {
    display: table-header-group;
  }
  tr,
  img {
    page-break-inside: avoid;
  }
  img {
    max-width: 100% !important;
  }
  @page {
    margin-top: 2cm;
    margin-bottom: 2cm;
  }
  p,
  h2,
  h3 {
    orphans: 3;
    widows: 3;
  }
  h2,
  h3 {
    page-break-after: avoid;
  }
  select {
    background: #fff !important;
  }
  .navbar {
    display: none;
  }
  .table td,
  .table th {
    background-color: #fff !important;
  }
  .btn > .caret,
  .dropup > .btn > .caret {
    border-top-color: #000 !important;
  }
  .label {
    border: 1px solid #000;
  }
  .table {
    border-collapse: collapse !important;
  }
  .table-bordered th,
  .table-bordered td {
    border: 1px solid #ddd !important;
  }
}
*,
*:before,
*:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
html {
  font-size: 62.5%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
body {
  font-family: "Helvetica Neue", Helvetica, "Noto Sans", sans-serif, Arial, sans-serif;
  font-size: 12px;
  line-height: 1.42857143;
  color: #949494;
  background-color: #ffffff;
}
input,
button,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
a {
  color: #373e4a;
  text-decoration: none;
}
a:hover,
a:focus {
  color: #818da2;
}
a:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
img {
  vertical-align: middle;
}
.img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}
.img-rounded {
  border-radius: 3px;
}
.img-thumbnail {
  padding: 2px;
  line-height: 1.42857143;
  background-color: #ffffff;
  border: 1px solid #ededf0;
  border-radius: 3px;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto;
}
.img-circle {
  border-radius: 50%;
}
hr {
  margin-top: 17px;
  margin-bottom: 17px;
  border: 0;
  border-top: 1px solid #eeeeee;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
figure {
  margin: 0;
}
p {
  margin: 0 0 8.5px;
  font-size: 12px;
}
.lead {
  margin-bottom: 17px;
  font-size: 13px;
  font-weight: 200;
  line-height: 1.4;
}
@media (min-width: 768px) {
  .lead {
    font-size: 18px;
  }
}
small,
.small {
  font-size: 85%;
}
cite {
  font-style: normal;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}
.text-justify {
  text-align: justify;
}
.text-muted {
  color: #999999;
}
.text-primary {
  color: #949494;
}
a.text-primary:hover {
  color: #7b7b7b;
}
.text-success {
  color: #045702;
}
a.text-success:hover {
  color: #022501;
}
.text-info {
  color: #2c7ea1;
}
a.text-info:hover {
  color: #215f79;
}
.text-warning {
  color: #574802;
}
a.text-warning:hover {
  color: #251f01;
}
.text-danger {
  color: #ac1818;
}
a.text-danger:hover {
  color: #7f1212;
}
.bg-primary {
  color: #fff;
  background-color: #949494;
}
a.bg-primary:hover {
  background-color: #7b7b7b;
}
.bg-success {
  background-color: #bdedbc;
}
a.bg-success:hover {
  background-color: #95e294;
}
.bg-info {
  background-color: #c5e8f7;
}
a.bg-info:hover {
  background-color: #98d6f1;
}
.bg-warning {
  background-color: #ffefa4;
}
a.bg-warning:hover {
  background-color: #ffe671;
}
.bg-danger {
  background-color: #ffc9c9;
}
a.bg-danger:hover {
  background-color: #ff9696;
}
h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  font-family: inherit;
  font-weight: 500;
  line-height: 1.1;
  color: #373e4a;
}
h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small,
.h1 small,
.h2 small,
.h3 small,
.h4 small,
.h5 small,
.h6 small,
h1 .small,
h2 .small,
h3 .small,
h4 .small,
h5 .small,
h6 .small,
.h1 .small,
.h2 .small,
.h3 .small,
.h4 .small,
.h5 .small,
.h6 .small {
  font-weight: normal;
  line-height: 1;
  color: #999999;
}
h1,
h2,
h3 {
  margin-top: 17px;
  margin-bottom: 8.5px;
}
h1 small,
h2 small,
h3 small,
h1 .small,
h2 .small,
h3 .small {
  font-size: 65%;
}
h4,
h5,
h6 {
  margin-top: 8.5px;
  margin-bottom: 8.5px;
}
h4 small,
h5 small,
h6 small,
h4 .small,
h5 .small,
h6 .small {
  font-size: 75%;
}
h1,
.h1 {
  font-size: 31px;
}
h2,
.h2 {
  font-size: 25px;
}
h3,
.h3 {
  font-size: 21px;
}
h4,
.h4 {
  font-size: 15px;
}
h5,
.h5 {
  font-size: 12px;
}
h6,
.h6 {
  font-size: 11px;
}
.bg-primary {
  color: #fff;
  background-color: #949494;
}
a.bg-primary:hover {
  background-color: #7b7b7b;
}
.bg-warning {
  background-color: #ffefa4;
}
a.bg-warning:hover {
  background-color: #ffe671;
}
.bg-danger {
  background-color: #ffc9c9;
}
a.bg-danger:hover {
  background-color: #ff9696;
}
.bg-success {
  background-color: #bdedbc;
}
a.bg-success:hover {
  background-color: #95e294;
}
.bg-info {
  background-color: #c5e8f7;
}
a.bg-info:hover {
  background-color: #98d6f1;
}
.page-header {
  padding-bottom: 7.5px;
  margin: 34px 0 17px;
  border-bottom: 1px solid #eeeeee;
}
ul,
ol {
  margin-top: 0;
  margin-bottom: 8.5px;
}
ul ul,
ol ul,
ul ol,
ol ol {
  margin-bottom: 0;
}
.list-unstyled {
  padding-left: 0;
  list-style: none;
}
.list-inline {
  padding-left: 0;
  list-style: none;
}
.list-inline > li {
  display: inline-block;
  padding-left: 5px;
  padding-right: 5px;
}
.list-inline > li:first-child {
  padding-left: 0;
}
dl {
  margin-bottom: 17px;
}
dt,
dd {
  line-height: 1.42857143;
}
dt {
  font-weight: bold;
}
dd {
  margin-left: 0;
}
@media (min-width: 768px) {
  .dl-horizontal dt {
    float: left;
    width: 160px;
    clear: left;
    text-align: right;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .dl-horizontal dd {
    margin-left: 180px;
  }
  .dl-horizontal dd:before,
  .dl-horizontal dd:after {
    content: " ";
    /* 1 */
    display: table;
    /* 2 */
  }
  .dl-horizontal dd:after {
    clear: both;
  }
  .dl-horizontal dd:before,
  .dl-horizontal dd:after {
    content: " ";
    /* 1 */
    display: table;
    /* 2 */
  }
  .dl-horizontal dd:after {
    clear: both;
  }
}
abbr[title],
abbr[data-original-title] {
  cursor: help;
  border-bottom: 1px dotted #999999;
}
abbr.initialism {
  font-size: 90%;
  text-transform: uppercase;
}
blockquote {
  padding: 8.5px 17px;
  margin: 0 0 17px;
  border-left: 5px solid #eeeeee;
}
blockquote p {
  font-size: 15px;
  font-weight: 300;
  line-height: 1.25;
}
blockquote p:last-child {
  margin-bottom: 0;
}
blockquote small {
  display: block;
  line-height: 1.42857143;
  color: #999999;
}
blockquote small:before {
  content: '\2014 \00A0';
}
blockquote.pull-right {
  padding-right: 15px;
  padding-left: 0;
  border-right: 5px solid #eeeeee;
  border-left: 0;
}
blockquote.pull-right p,
blockquote.pull-right small,
blockquote.pull-right .small {
  text-align: right;
}
blockquote.pull-right small:before,
blockquote.pull-right .small:before {
  content: '';
}
blockquote.pull-right small:after,
blockquote.pull-right .small:after {
  content: '\00A0 \2014';
}
blockquote:before,
blockquote:after {
  content: "";
}
address {
  margin-bottom: 17px;
  font-style: normal;
  line-height: 1.42857143;
}
code,
kbd,
pre,
samp {
  font-family: "Noto Sans", sans-serif, Monaco, Menlo, Consolas, "Courier New", monospace;
}
code {
  padding: 2px 4px;
  font-size: 90%;
  color: #7d8086;
  background-color: #ebebeb;
  white-space: nowrap;
  border-radius: 3px;
}
kbd {
  padding: 2px 4px;
  font-size: 90%;
  color: #ffffff;
  background-color: #333333;
  border-radius: 2px;
  box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.25);
}
pre {
  display: block;
  padding: 8px;
  margin: 0 0 8.5px;
  font-size: 11px;
  line-height: 1.42857143;
  word-break: break-all;
  word-wrap: break-word;
  color: #7d8086;
  background-color: #ebebeb;
  border: 1px solid #d9d9d9;
  border-radius: 3px;
}
pre code {
  padding: 0;
  font-size: inherit;
  color: inherit;
  white-space: pre-wrap;
  background-color: transparent;
  border-radius: 0;
}
.pre-scrollable {
  max-height: 340px;
  overflow-y: scroll;
}
.container {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
.container:before,
.container:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.container:after {
  clear: both;
}
.container:before,
.container:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.container:after {
  clear: both;
}
@media (min-width: 768px) {
  .container {
    width: 750px;
  }
}
@media (min-width: 992px) {
  .container {
    width: 970px;
  }
}
@media (min-width: 1200px) {
  .container {
    width: 1170px;
  }
}
.container-fluid {
  margin-right: auto;
  margin-left: auto;
  padding-left: 15px;
  padding-right: 15px;
}
.container-fluid:before,
.container-fluid:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.container-fluid:after {
  clear: both;
}
.container-fluid:before,
.container-fluid:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.container-fluid:after {
  clear: both;
}
.row {
  margin-left: -15px;
  margin-right: -15px;
}
.row:before,
.row:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.row:after {
  clear: both;
}
.row:before,
.row:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.row:after {
  clear: both;
}
.col-xs-1, .col-sm-1, .col-md-1, .col-lg-1, .col-xs-2, .col-sm-2, .col-md-2, .col-lg-2, .col-xs-3, .col-sm-3, .col-md-3, .col-lg-3, .col-xs-4, .col-sm-4, .col-md-4, .col-lg-4, .col-xs-5, .col-sm-5, .col-md-5, .col-lg-5, .col-xs-6, .col-sm-6, .col-md-6, .col-lg-6, .col-xs-7, .col-sm-7, .col-md-7, .col-lg-7, .col-xs-8, .col-sm-8, .col-md-8, .col-lg-8, .col-xs-9, .col-sm-9, .col-md-9, .col-lg-9, .col-xs-10, .col-sm-10, .col-md-10, .col-lg-10, .col-xs-11, .col-sm-11, .col-md-11, .col-lg-11, .col-xs-12, .col-sm-12, .col-md-12, .col-lg-12 {
  position: relative;
  min-height: 1px;
  padding-left: 15px;
  padding-right: 15px;
}
.col-xs-1, .col-xs-2, .col-xs-3, .col-xs-4, .col-xs-5, .col-xs-6, .col-xs-7, .col-xs-8, .col-xs-9, .col-xs-10, .col-xs-11 {
  float: left;
}
.col-xs-12 {
  width: 100%;
}
.col-xs-11 {
  width: 91.66666667%;
}
.col-xs-10 {
  width: 83.33333333%;
}
.col-xs-9 {
  width: 75%;
}
.col-xs-8 {
  width: 66.66666667%;
}
.col-xs-7 {
  width: 58.33333333%;
}
.col-xs-6 {
  width: 50%;
}
.col-xs-5 {
  width: 41.66666667%;
}
.col-xs-4 {
  width: 33.33333333%;
}
.col-xs-3 {
  width: 25%;
}
.col-xs-2 {
  width: 16.66666667%;
}
.col-xs-1 {
  width: 8.33333333%;
}
.col-xs-pull-12 {
  right: 100%;
}
.col-xs-pull-11 {
  right: 91.66666667%;
}
.col-xs-pull-10 {
  right: 83.33333333%;
}
.col-xs-pull-9 {
  right: 75%;
}
.col-xs-pull-8 {
  right: 66.66666667%;
}
.col-xs-pull-7 {
  right: 58.33333333%;
}
.col-xs-pull-6 {
  right: 50%;
}
.col-xs-pull-5 {
  right: 41.66666667%;
}
.col-xs-pull-4 {
  right: 33.33333333%;
}
.col-xs-pull-3 {
  right: 25%;
}
.col-xs-pull-2 {
  right: 16.66666667%;
}
.col-xs-pull-1 {
  right: 8.33333333%;
}
.col-xs-pull-0 {
  right: 0%;
}
.col-xs-push-12 {
  left: 100%;
}
.col-xs-push-11 {
  left: 91.66666667%;
}
.col-xs-push-10 {
  left: 83.33333333%;
}
.col-xs-push-9 {
  left: 75%;
}
.col-xs-push-8 {
  left: 66.66666667%;
}
.col-xs-push-7 {
  left: 58.33333333%;
}
.col-xs-push-6 {
  left: 50%;
}
.col-xs-push-5 {
  left: 41.66666667%;
}
.col-xs-push-4 {
  left: 33.33333333%;
}
.col-xs-push-3 {
  left: 25%;
}
.col-xs-push-2 {
  left: 16.66666667%;
}
.col-xs-push-1 {
  left: 8.33333333%;
}
.col-xs-push-0 {
  left: 0%;
}
.col-xs-offset-12 {
  margin-left: 100%;
}
.col-xs-offset-11 {
  margin-left: 91.66666667%;
}
.col-xs-offset-10 {
  margin-left: 83.33333333%;
}
.col-xs-offset-9 {
  margin-left: 75%;
}
.col-xs-offset-8 {
  margin-left: 66.66666667%;
}
.col-xs-offset-7 {
  margin-left: 58.33333333%;
}
.col-xs-offset-6 {
  margin-left: 50%;
}
.col-xs-offset-5 {
  margin-left: 41.66666667%;
}
.col-xs-offset-4 {
  margin-left: 33.33333333%;
}
.col-xs-offset-3 {
  margin-left: 25%;
}
.col-xs-offset-2 {
  margin-left: 16.66666667%;
}
.col-xs-offset-1 {
  margin-left: 8.33333333%;
}
.col-xs-offset-0 {
  margin-left: 0%;
}
@media (min-width: 768px) {
  .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11 {
    float: left;
  }
  .col-sm-12 {
    width: 100%;
  }
  .col-sm-11 {
    width: 91.66666667%;
  }
  .col-sm-10 {
    width: 83.33333333%;
  }
  .col-sm-9 {
    width: 75%;
  }
  .col-sm-8 {
    width: 66.66666667%;
  }
  .col-sm-7 {
    width: 58.33333333%;
  }
  .col-sm-6 {
    width: 50%;
  }
  .col-sm-5 {
    width: 41.66666667%;
  }
  .col-sm-4 {
    width: 33.33333333%;
  }
  .col-sm-3 {
    width: 25%;
  }
  .col-sm-2 {
    width: 16.66666667%;
  }
  .col-sm-1 {
    width: 8.33333333%;
  }
  .col-sm-pull-12 {
    right: 100%;
  }
  .col-sm-pull-11 {
    right: 91.66666667%;
  }
  .col-sm-pull-10 {
    right: 83.33333333%;
  }
  .col-sm-pull-9 {
    right: 75%;
  }
  .col-sm-pull-8 {
    right: 66.66666667%;
  }
  .col-sm-pull-7 {
    right: 58.33333333%;
  }
  .col-sm-pull-6 {
    right: 50%;
  }
  .col-sm-pull-5 {
    right: 41.66666667%;
  }
  .col-sm-pull-4 {
    right: 33.33333333%;
  }
  .col-sm-pull-3 {
    right: 25%;
  }
  .col-sm-pull-2 {
    right: 16.66666667%;
  }
  .col-sm-pull-1 {
    right: 8.33333333%;
  }
  .col-sm-pull-0 {
    right: 0%;
  }
  .col-sm-push-12 {
    left: 100%;
  }
  .col-sm-push-11 {
    left: 91.66666667%;
  }
  .col-sm-push-10 {
    left: 83.33333333%;
  }
  .col-sm-push-9 {
    left: 75%;
  }
  .col-sm-push-8 {
    left: 66.66666667%;
  }
  .col-sm-push-7 {
    left: 58.33333333%;
  }
  .col-sm-push-6 {
    left: 50%;
  }
  .col-sm-push-5 {
    left: 41.66666667%;
  }
  .col-sm-push-4 {
    left: 33.33333333%;
  }
  .col-sm-push-3 {
    left: 25%;
  }
  .col-sm-push-2 {
    left: 16.66666667%;
  }
  .col-sm-push-1 {
    left: 8.33333333%;
  }
  .col-sm-push-0 {
    left: 0%;
  }
  .col-sm-offset-12 {
    margin-left: 100%;
  }
  .col-sm-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-sm-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-sm-offset-9 {
    margin-left: 75%;
  }
  .col-sm-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-sm-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-sm-offset-6 {
    margin-left: 50%;
  }
  .col-sm-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-sm-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-sm-offset-3 {
    margin-left: 25%;
  }
  .col-sm-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-sm-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-sm-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 992px) {
  .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11 {
    float: left;
  }
  .col-md-12 {
    width: 100%;
  }
  .col-md-11 {
    width: 91.66666667%;
  }
  .col-md-10 {
    width: 83.33333333%;
  }
  .col-md-9 {
    width: 75%;
  }
  .col-md-8 {
    width: 66.66666667%;
  }
  .col-md-7 {
    width: 58.33333333%;
  }
  .col-md-6 {
    width: 50%;
  }
  .col-md-5 {
    width: 41.66666667%;
  }
  .col-md-4 {
    width: 33.33333333%;
  }
  .col-md-3 {
    width: 25%;
  }
  .col-md-2 {
    width: 16.66666667%;
  }
  .col-md-1 {
    width: 8.33333333%;
  }
  .col-md-pull-12 {
    right: 100%;
  }
  .col-md-pull-11 {
    right: 91.66666667%;
  }
  .col-md-pull-10 {
    right: 83.33333333%;
  }
  .col-md-pull-9 {
    right: 75%;
  }
  .col-md-pull-8 {
    right: 66.66666667%;
  }
  .col-md-pull-7 {
    right: 58.33333333%;
  }
  .col-md-pull-6 {
    right: 50%;
  }
  .col-md-pull-5 {
    right: 41.66666667%;
  }
  .col-md-pull-4 {
    right: 33.33333333%;
  }
  .col-md-pull-3 {
    right: 25%;
  }
  .col-md-pull-2 {
    right: 16.66666667%;
  }
  .col-md-pull-1 {
    right: 8.33333333%;
  }
  .col-md-pull-0 {
    right: 0%;
  }
  .col-md-push-12 {
    left: 100%;
  }
  .col-md-push-11 {
    left: 91.66666667%;
  }
  .col-md-push-10 {
    left: 83.33333333%;
  }
  .col-md-push-9 {
    left: 75%;
  }
  .col-md-push-8 {
    left: 66.66666667%;
  }
  .col-md-push-7 {
    left: 58.33333333%;
  }
  .col-md-push-6 {
    left: 50%;
  }
  .col-md-push-5 {
    left: 41.66666667%;
  }
  .col-md-push-4 {
    left: 33.33333333%;
  }
  .col-md-push-3 {
    left: 25%;
  }
  .col-md-push-2 {
    left: 16.66666667%;
  }
  .col-md-push-1 {
    left: 8.33333333%;
  }
  .col-md-push-0 {
    left: 0%;
  }
  .col-md-offset-12 {
    margin-left: 100%;
  }
  .col-md-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-md-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-md-offset-9 {
    margin-left: 75%;
  }
  .col-md-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-md-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-md-offset-6 {
    margin-left: 50%;
  }
  .col-md-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-md-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-md-offset-3 {
    margin-left: 25%;
  }
  .col-md-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-md-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-md-offset-0 {
    margin-left: 0%;
  }
}
@media (min-width: 1200px) {
  .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11 {
    float: left;
  }
  .col-lg-12 {
    width: 100%;
  }
  .col-lg-11 {
    width: 91.66666667%;
  }
  .col-lg-10 {
    width: 83.33333333%;
  }
  .col-lg-9 {
    width: 75%;
  }
  .col-lg-8 {
    width: 66.66666667%;
  }
  .col-lg-7 {
    width: 58.33333333%;
  }
  .col-lg-6 {
    width: 50%;
  }
  .col-lg-5 {
    width: 41.66666667%;
  }
  .col-lg-4 {
    width: 33.33333333%;
  }
  .col-lg-3 {
    width: 25%;
  }
  .col-lg-2 {
    width: 16.66666667%;
  }
  .col-lg-1 {
    width: 8.33333333%;
  }
  .col-lg-pull-12 {
    right: 100%;
  }
  .col-lg-pull-11 {
    right: 91.66666667%;
  }
  .col-lg-pull-10 {
    right: 83.33333333%;
  }
  .col-lg-pull-9 {
    right: 75%;
  }
  .col-lg-pull-8 {
    right: 66.66666667%;
  }
  .col-lg-pull-7 {
    right: 58.33333333%;
  }
  .col-lg-pull-6 {
    right: 50%;
  }
  .col-lg-pull-5 {
    right: 41.66666667%;
  }
  .col-lg-pull-4 {
    right: 33.33333333%;
  }
  .col-lg-pull-3 {
    right: 25%;
  }
  .col-lg-pull-2 {
    right: 16.66666667%;
  }
  .col-lg-pull-1 {
    right: 8.33333333%;
  }
  .col-lg-pull-0 {
    right: 0%;
  }
  .col-lg-push-12 {
    left: 100%;
  }
  .col-lg-push-11 {
    left: 91.66666667%;
  }
  .col-lg-push-10 {
    left: 83.33333333%;
  }
  .col-lg-push-9 {
    left: 75%;
  }
  .col-lg-push-8 {
    left: 66.66666667%;
  }
  .col-lg-push-7 {
    left: 58.33333333%;
  }
  .col-lg-push-6 {
    left: 50%;
  }
  .col-lg-push-5 {
    left: 41.66666667%;
  }
  .col-lg-push-4 {
    left: 33.33333333%;
  }
  .col-lg-push-3 {
    left: 25%;
  }
  .col-lg-push-2 {
    left: 16.66666667%;
  }
  .col-lg-push-1 {
    left: 8.33333333%;
  }
  .col-lg-push-0 {
    left: 0%;
  }
  .col-lg-offset-12 {
    margin-left: 100%;
  }
  .col-lg-offset-11 {
    margin-left: 91.66666667%;
  }
  .col-lg-offset-10 {
    margin-left: 83.33333333%;
  }
  .col-lg-offset-9 {
    margin-left: 75%;
  }
  .col-lg-offset-8 {
    margin-left: 66.66666667%;
  }
  .col-lg-offset-7 {
    margin-left: 58.33333333%;
  }
  .col-lg-offset-6 {
    margin-left: 50%;
  }
  .col-lg-offset-5 {
    margin-left: 41.66666667%;
  }
  .col-lg-offset-4 {
    margin-left: 33.33333333%;
  }
  .col-lg-offset-3 {
    margin-left: 25%;
  }
  .col-lg-offset-2 {
    margin-left: 16.66666667%;
  }
  .col-lg-offset-1 {
    margin-left: 8.33333333%;
  }
  .col-lg-offset-0 {
    margin-left: 0%;
  }
}
table {
  max-width: 100%;
  background-color: transparent;
}
th {
  text-align: left;
  font-weight: 400;
  color: #303641;
}
.table {
  width: 100%;
  margin-bottom: 17px;
}
.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #ebebeb;
}
.table > thead > tr > th .progress,
.table > tbody > tr > th .progress,
.table > tfoot > tr > th .progress,
.table > thead > tr > td .progress,
.table > tbody > tr > td .progress,
.table > tfoot > tr > td .progress {
  margin-bottom: 0;
}
.table > thead > tr > th .label,
.table > tbody > tr > th .label,
.table > tfoot > tr > th .label,
.table > thead > tr > td .label,
.table > tbody > tr > td .label,
.table > tfoot > tr > td .label {
  margin-left: 5px;
  margin-right: 5px;
  padding-left: 10px;
  padding-right: 10px;
}
.table > thead > tr > th {
  vertical-align: bottom;
  border-bottom: 2px solid #ebebeb;
}
.table > caption + thead > tr:first-child > th,
.table > colgroup + thead > tr:first-child > th,
.table > thead:first-child > tr:first-child > th,
.table > caption + thead > tr:first-child > td,
.table > colgroup + thead > tr:first-child > td,
.table > thead:first-child > tr:first-child > td {
  border-top: 0;
}
.table > tbody + tbody {
  border-top: 2px solid #ebebeb;
}
.table .table {
  background-color: #ffffff;
}
.table-condensed > thead > tr > th,
.table-condensed > tbody > tr > th,
.table-condensed > tfoot > tr > th,
.table-condensed > thead > tr > td,
.table-condensed > tbody > tr > td,
.table-condensed > tfoot > tr > td {
  padding: 5px;
}
.table-bordered {
  border: 1px solid #ebebeb;
}
.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 1px solid #ebebeb;
}
.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
  background-color: #f5f5f6;
  border-bottom-width: 1px;
  color: #a6a7aa;
}
.table-bordered > tfoot > tr > th,
.table-bordered > tfoot > tr > td {
  background-color: #f5f5f6;
  border-top-width: 1px;
  color: #a6a7aa;
}
.table-striped > tbody > tr:nth-child(odd) > td,
.table-striped > tbody > tr:nth-child(odd) > th {
  background-color: #f8f8f8;
}
.table-hover > tbody > tr:hover > td,
.table-hover > tbody > tr:hover > th {
  background-color: #f2f2f4;
}
table col[class*="col-"] {
  float: none;
  display: table-column;
}
table td[class*="col-"],
table th[class*="col-"] {
  float: none;
  display: table-cell;
}
.table > thead > tr > td.active,
.table > tbody > tr > td.active,
.table > tfoot > tr > td.active,
.table > thead > tr > th.active,
.table > tbody > tr > th.active,
.table > tfoot > tr > th.active,
.table > thead > tr.active > td,
.table > tbody > tr.active > td,
.table > tfoot > tr.active > td,
.table > thead > tr.active > th,
.table > tbody > tr.active > th,
.table > tfoot > tr.active > th {
  background-color: #f2f2f4;
}
.table > thead > tr > td.active,
.table > tbody > tr > td.active,
.table > tfoot > tr > td.active,
.table > thead > tr > th.active,
.table > tbody > tr > th.active,
.table > tfoot > tr > th.active,
.table > thead > tr.active > td,
.table > tbody > tr.active > td,
.table > tfoot > tr.active > td,
.table > thead > tr.active > th,
.table > tbody > tr.active > th,
.table > tfoot > tr.active > th {
  background-color: #f2f2f4;
}
.table-hover > tbody > tr > td.active:hover,
.table-hover > tbody > tr > th.active:hover,
.table-hover > tbody > tr.active:hover > td,
.table-hover > tbody > tr.active:hover > th {
  background-color: #e5e5e8;
}
.table > thead > tr > td.success,
.table > tbody > tr > td.success,
.table > tfoot > tr > td.success,
.table > thead > tr > th.success,
.table > tbody > tr > th.success,
.table > tfoot > tr > th.success,
.table > thead > tr.success > td,
.table > tbody > tr.success > td,
.table > tfoot > tr.success > td,
.table > thead > tr.success > th,
.table > tbody > tr.success > th,
.table > tfoot > tr.success > th {
  background-color: #bdedbc;
}
.table-hover > tbody > tr > td.success:hover,
.table-hover > tbody > tr > th.success:hover,
.table-hover > tbody > tr.success:hover > td,
.table-hover > tbody > tr.success:hover > th {
  background-color: #a9e8a8;
}
.table > thead > tr > td.info,
.table > tbody > tr > td.info,
.table > tfoot > tr > td.info,
.table > thead > tr > th.info,
.table > tbody > tr > th.info,
.table > tfoot > tr > th.info,
.table > thead > tr.info > td,
.table > tbody > tr.info > td,
.table > tfoot > tr.info > td,
.table > thead > tr.info > th,
.table > tbody > tr.info > th,
.table > tfoot > tr.info > th {
  background-color: #c5e8f7;
}
.table-hover > tbody > tr > td.info:hover,
.table-hover > tbody > tr > th.info:hover,
.table-hover > tbody > tr.info:hover > td,
.table-hover > tbody > tr.info:hover > th {
  background-color: #afdff4;
}
.table > thead > tr > td.warning,
.table > tbody > tr > td.warning,
.table > tfoot > tr > td.warning,
.table > thead > tr > th.warning,
.table > tbody > tr > th.warning,
.table > tfoot > tr > th.warning,
.table > thead > tr.warning > td,
.table > tbody > tr.warning > td,
.table > tfoot > tr.warning > td,
.table > thead > tr.warning > th,
.table > tbody > tr.warning > th,
.table > tfoot > tr.warning > th {
  background-color: #ffefa4;
}
.table-hover > tbody > tr > td.warning:hover,
.table-hover > tbody > tr > th.warning:hover,
.table-hover > tbody > tr.warning:hover > td,
.table-hover > tbody > tr.warning:hover > th {
  background-color: #ffeb8a;
}
.table > thead > tr > td.danger,
.table > tbody > tr > td.danger,
.table > tfoot > tr > td.danger,
.table > thead > tr > th.danger,
.table > tbody > tr > th.danger,
.table > tfoot > tr > th.danger,
.table > thead > tr.danger > td,
.table > tbody > tr.danger > td,
.table > tfoot > tr.danger > td,
.table > thead > tr.danger > th,
.table > tbody > tr.danger > th,
.table > tfoot > tr.danger > th {
  background-color: #ffc9c9;
}
.table-hover > tbody > tr > td.danger:hover,
.table-hover > tbody > tr > th.danger:hover,
.table-hover > tbody > tr.danger:hover > td,
.table-hover > tbody > tr.danger:hover > th {
  background-color: #ffafaf;
}
@media (max-width: 767px) {
  .table-responsive {
    width: 100%;
    margin-bottom: 12.75px;
    overflow-y: hidden;
    overflow-x: scroll;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    border: 1px solid #ebebeb;
    -webkit-overflow-scrolling: touch;
  }
  .table-responsive > .table {
    margin-bottom: 0;
  }
  .table-responsive > .table > thead > tr > th,
  .table-responsive > .table > tbody > tr > th,
  .table-responsive > .table > tfoot > tr > th,
  .table-responsive > .table > thead > tr > td,
  .table-responsive > .table > tbody > tr > td,
  .table-responsive > .table > tfoot > tr > td {
    white-space: nowrap;
  }
  .table-responsive > .table-bordered {
    border: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:first-child,
  .table-responsive > .table-bordered > tbody > tr > th:first-child,
  .table-responsive > .table-bordered > tfoot > tr > th:first-child,
  .table-responsive > .table-bordered > thead > tr > td:first-child,
  .table-responsive > .table-bordered > tbody > tr > td:first-child,
  .table-responsive > .table-bordered > tfoot > tr > td:first-child {
    border-left: 0;
  }
  .table-responsive > .table-bordered > thead > tr > th:last-child,
  .table-responsive > .table-bordered > tbody > tr > th:last-child,
  .table-responsive > .table-bordered > tfoot > tr > th:last-child,
  .table-responsive > .table-bordered > thead > tr > td:last-child,
  .table-responsive > .table-bordered > tbody > tr > td:last-child,
  .table-responsive > .table-bordered > tfoot > tr > td:last-child {
    border-right: 0;
  }
  .table-responsive > .table-bordered > tbody > tr:last-child > th,
  .table-responsive > .table-bordered > tfoot > tr:last-child > th,
  .table-responsive > .table-bordered > tbody > tr:last-child > td,
  .table-responsive > .table-bordered > tfoot > tr:last-child > td {
    border-bottom: 0;
  }
}
table > tbody > tr.highlight > td,
table > tbody > tr.highlight > th {
  background-color: #f1f2f4 !important;
  color: #303641;
}
.table > thead > tr > .middle-align,
.table > tbody > tr > .middle-align,
.table > tfoot > tr > .middle-align {
  vertical-align: middle;
}
fieldset {
  padding: 0;
  margin: 0;
  border: 0;
  min-width: 0;
}
legend {
  display: block;
  width: 100%;
  padding: 0;
  margin-bottom: 17px;
  font-size: 18px;
  line-height: inherit;
  color: #7d8086;
  border: 0;
  border-bottom: 1px solid #e5e5e5;
}
label {
  display: inline-block;
  margin-bottom: 5px;
}
input[type="search"] {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
input[type="radio"],
input[type="checkbox"] {
  margin: 4px 0 0;
  margin-top: 1px \9;
  /* IE8-9 */
  line-height: normal;
}
input[type="file"] {
  display: block;
}
input[type="range"] {
  display: block;
  width: 100%;
}
select[multiple],
select[size] {
  height: auto;
}
input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
input[type="file"]:focus,
input[type="radio"]:focus,
input[type="checkbox"]:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
output {
  display: block;
  padding-top: 7px;
  font-size: 12px;
  line-height: 1.42857143;
  color: #555555;
}
.form-control {
  display: block;
  width: 100%;
  height: 31px;
  padding: 6px 12px;
  font-size: 12px;
  line-height: 1.42857143;
  color: #555555;
  background-color: #ffffff;
  background-image: none;
  border: 1px solid #ebebeb;
  border-radius: 3px;
  -webkit-transition: border-color ease-in-out .15s, -webkit-box-shadow ease-in-out .15s;
  -moz-transition: border-color ease-in-out .15s, -moz-box-shadow ease-in-out .15s;
  -o-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}
.form-control:focus {
  border-color: #c8cdd7;
  outline: 0;
  -moz-box-shadow:  0 2px 1px rgba(203, 208, 217, 0.08);
  -webkit-box-shadow:  0 2px 1px rgba(203, 208, 217, 0.08);
  box-shadow:  0 2px 1px rgba(203, 208, 217, 0.08);
}
.form-control::-webkit-input-placeholder {
  color: #aaaaaa;
}
.form-control:-moz-placeholder {
  color: #aaaaaa;
}
.form-control::-moz-placeholder {
  color: #aaaaaa;
}
.form-control:-ms-input-placeholder {
  color: #aaaaaa;
}
.form-control:-moz-placeholder {
  color: #aaaaaa;
}
.form-control::-moz-placeholder {
  color: #aaaaaa;
}
.form-control:-ms-input-placeholder {
  color: #aaaaaa;
}
.form-control::-webkit-input-placeholder {
  color: #aaaaaa;
}
.form-control[disabled],
.form-control[readonly],
fieldset[disabled] .form-control {
  cursor: not-allowed;
  background-color: #eeeeee;
}
textarea.form-control {
  height: auto;
}
input[type="date"] {
  line-height: 31px;
}
.form-group {
  margin-bottom: 15px;
}
.radio,
.checkbox {
  display: block;
  min-height: 17px;
  margin-top: 10px;
  margin-bottom: 10px;
  padding-left: 20px;
}
.radio label,
.checkbox label {
  display: inline;
  font-weight: normal;
  cursor: pointer;
}
.radio input[type="radio"],
.radio-inline input[type="radio"],
.checkbox input[type="checkbox"],
.checkbox-inline input[type="checkbox"] {
  float: left;
  margin-left: -20px;
  margin-top: 1px;
}
.radio + .radio,
.checkbox + .checkbox {
  margin-top: -5px;
}
.radio-inline,
.checkbox-inline {
  display: inline-block;
  padding-left: 20px;
  margin-bottom: 0;
  vertical-align: middle;
  font-weight: normal;
  cursor: pointer;
}
.radio-inline + .radio-inline,
.checkbox-inline + .checkbox-inline {
  margin-top: 0;
  margin-left: 10px;
}
input[type="radio"][disabled],
input[type="checkbox"][disabled],
.radio[disabled],
.radio-inline[disabled],
.checkbox[disabled],
.checkbox-inline[disabled],
fieldset[disabled] input[type="radio"],
fieldset[disabled] input[type="checkbox"],
fieldset[disabled] .radio,
fieldset[disabled] .radio-inline,
fieldset[disabled] .checkbox,
fieldset[disabled] .checkbox-inline {
  cursor: not-allowed;
}
.input-sm {
  height: 28px;
  padding: 5px 10px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
select.input-sm {
  height: 28px;
  line-height: 28px;
}
textarea.input-sm,
select[multiple].input-sm {
  height: auto;
}
.input-lg {
  height: 41px;
  padding: 10px 16px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
select.input-lg {
  height: 41px;
  line-height: 41px;
}
textarea.input-lg,
select[multiple].input-lg {
  height: auto;
}
.has-feedback {
  position: relative;
}
.has-feedback .form-control {
  padding-right: 38.75px;
}
.has-feedback .form-control-feedback {
  position: absolute;
  top: 22px;
  right: 0;
  display: block;
  width: 31px;
  height: 31px;
  line-height: 31px;
  text-align: center;
}
.has-warning .help-block,
.has-warning .control-label,
.has-warning .radio,
.has-warning .checkbox,
.has-warning .radio-inline,
.has-warning .checkbox-inline {
  color: #ffd40b;
}
.has-warning .form-control {
  border-color: #ffd78a;
}
.has-warning .form-control:focus {
  border-color: #ffc658;
}
.has-warning .input-group-addon {
  color: #ffd40b;
  border-color: #ffd78a;
  background-color: #ffefa4;
}
.has-error .help-block,
.has-error .control-label,
.has-error .radio,
.has-error .checkbox,
.has-error .radio-inline,
.has-error .checkbox-inline {
  color: #ff3030;
}
.has-error .form-control {
  border-color: #ffafbd;
}
.has-error .form-control:focus {
  border-color: #ff7c92;
}
.has-error .input-group-addon {
  color: #ff3030;
  border-color: #ffafbd;
  background-color: #ffc9c9;
}
.has-success .help-block,
.has-success .control-label,
.has-success .radio,
.has-success .checkbox,
.has-success .radio-inline,
.has-success .checkbox-inline {
  color: #46cd43;
}
.has-success .form-control {
  border-color: #b4e8a8;
}
.has-success .form-control:focus {
  border-color: #91dd80;
}
.has-success .input-group-addon {
  color: #46cd43;
  border-color: #b4e8a8;
  background-color: #bdedbc;
}
.form-control-static {
  margin-bottom: 0;
}
.help-block {
  display: block;
  margin-top: 5px;
  margin-bottom: 10px;
  color: #d4d4d4;
}
@media (min-width: 768px) {
  .form-inline .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .form-inline .input-group > .form-control {
    width: 100%;
  }
  .form-inline .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .form-inline .radio,
  .form-inline .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 0;
    vertical-align: middle;
  }
  .form-inline .radio input[type="radio"],
  .form-inline .checkbox input[type="checkbox"] {
    float: none;
    margin-left: 0;
  }
  .form-inline .has-feedback .form-control-feedback {
    top: 0;
  }
}
.form-horizontal .control-label,
.form-horizontal .radio,
.form-horizontal .checkbox,
.form-horizontal .radio-inline,
.form-horizontal .checkbox-inline {
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 7px;
}
.form-horizontal .radio,
.form-horizontal .checkbox {
  min-height: 24px;
}
.form-horizontal .form-group {
  margin-left: -15px;
  margin-right: -15px;
}
.form-horizontal .form-group:before,
.form-horizontal .form-group:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.form-horizontal .form-group:after {
  clear: both;
}
.form-horizontal .form-group:before,
.form-horizontal .form-group:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.form-horizontal .form-group:after {
  clear: both;
}
.form-horizontal .form-control-static {
  padding-top: 7px;
}
@media (min-width: 768px) {
  .form-horizontal .control-label {
    text-align: right;
  }
}
.form-horizontal .has-feedback .form-control-feedback {
  top: 0;
  right: 15px;
}
.bs-example > .btn,
.bs-example > .make-switch,
.bs-example > .btn-group {
  margin-top: 4px;
  margin-bottom: 4px;
  margin-right: 9px;
}
.bs-example.bs-baseline-top .btn,
.bs-example.bs-baseline-top .btn-group {
  vertical-align: top !important;
}
.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 6px 12px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}
.btn:focus {
  outline: thin dotted #333;
  outline: 5px auto -webkit-focus-ring-color;
  outline-offset: -2px;
}
.btn:hover,
.btn:focus {
  color: #303641;
  text-decoration: none;
  outline: none;
}
.btn:active,
.btn.active {
  outline: none;
  background-image: none;
  -moz-box-shadow: inset 0 0px 7px rgba(0, 0, 0, 0.225);
  -webkit-box-shadow: inset 0 0px 7px rgba(0, 0, 0, 0.225);
  box-shadow: inset 0 0px 7px rgba(0, 0, 0, 0.225);
  -moz-box-shadow: inset 0 0px 4px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: inset 0 0px 4px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 0px 4px rgba(0, 0, 0, 0.2);
}
.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
  cursor: not-allowed;
  pointer-events: none;
  -webkit-opacity: 0.65;
  -moz-opacity: 0.65;
  opacity: 0.65;
  filter: alpha(opacity=65);
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn.btn-icon {
  position: relative;
}
.btn.btn-icon i {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
}
.btn-default {
  color: #303641;
  background-color: #f0f0f1;
  border-color: #f0f0f1;
}
.btn-default:hover,
.btn-default:focus,
.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
  color: #303641;
  background-color: #dbdbdd;
  border-color: #d0d0d3;
}
.btn-default:active,
.btn-default.active,
.open .dropdown-toggle.btn-default {
  background-image: none;
}
.btn-default.disabled,
.btn-default[disabled],
fieldset[disabled] .btn-default,
.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled:active,
.btn-default[disabled]:active,
fieldset[disabled] .btn-default:active,
.btn-default.disabled.active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default.active {
  background-color: #f0f0f1;
  border-color: #f0f0f1;
}
.btn-default .badge {
  color: #f0f0f1;
  background-color: #303641;
}
.btn-default > .caret {
  border-top-color: #303641;
  border-bottom-color: #303641 !important;
}
.btn-default.dropdown-toggle {
  border-left-color: #dedee0;
}
.btn-default.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-default.btn-icon i {
  background-color: #dbdbdd;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-default.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-default.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-default.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-default.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-default.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-default.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-default.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-default.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-default.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-default.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-default.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-primary {
  color: #ffffff;
  background-color: #303641;
  border-color: #303641;
}
.btn-primary:hover,
.btn-primary:focus,
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
  color: #ffffff;
  background-color: #1f232a;
  border-color: #16191e;
}
.btn-primary:active,
.btn-primary.active,
.open .dropdown-toggle.btn-primary {
  background-image: none;
}
.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  background-color: #303641;
  border-color: #303641;
}
.btn-primary .badge {
  color: #303641;
  background-color: #ffffff;
}
.btn-primary > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-primary.dropdown-toggle {
  border-left-color: #21252c;
}
.btn-primary.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-primary.btn-icon i {
  background-color: #1f232a;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-primary.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-primary.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-primary.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-primary.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-primary.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-primary.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-primary.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-primary.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-primary.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-primary.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-primary.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-blue {
  color: #ffffff;
  background-color: #0072bc;
  border-color: #0072bc;
}
.btn-blue:hover,
.btn-blue:focus,
.btn-blue:active,
.btn-blue.active,
.open .dropdown-toggle.btn-blue {
  color: #ffffff;
  background-color: #005993;
  border-color: #004d7f;
}
.btn-blue:active,
.btn-blue.active,
.open .dropdown-toggle.btn-blue {
  background-image: none;
}
.btn-blue.disabled,
.btn-blue[disabled],
fieldset[disabled] .btn-blue,
.btn-blue.disabled:hover,
.btn-blue[disabled]:hover,
fieldset[disabled] .btn-blue:hover,
.btn-blue.disabled:focus,
.btn-blue[disabled]:focus,
fieldset[disabled] .btn-blue:focus,
.btn-blue.disabled:active,
.btn-blue[disabled]:active,
fieldset[disabled] .btn-blue:active,
.btn-blue.disabled.active,
.btn-blue[disabled].active,
fieldset[disabled] .btn-blue.active {
  background-color: #0072bc;
  border-color: #0072bc;
}
.btn-blue .badge {
  color: #0072bc;
  background-color: #ffffff;
}
.btn-blue > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-blue.dropdown-toggle {
  border-left-color: #005c98;
}
.btn-blue.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-blue.btn-icon i {
  background-color: #005993;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-blue.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-blue.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-blue.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-blue.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-blue.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-blue.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-blue.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-blue.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-blue.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-blue.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-blue.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-red {
  color: #ffffff;
  background-color: #d42020;
  border-color: #d42020;
}
.btn-red:hover,
.btn-red:focus,
.btn-red:active,
.btn-red.active,
.open .dropdown-toggle.btn-red {
  color: #ffffff;
  background-color: #b11b1b;
  border-color: #9f1818;
}
.btn-red:active,
.btn-red.active,
.open .dropdown-toggle.btn-red {
  background-image: none;
}
.btn-red.disabled,
.btn-red[disabled],
fieldset[disabled] .btn-red,
.btn-red.disabled:hover,
.btn-red[disabled]:hover,
fieldset[disabled] .btn-red:hover,
.btn-red.disabled:focus,
.btn-red[disabled]:focus,
fieldset[disabled] .btn-red:focus,
.btn-red.disabled:active,
.btn-red[disabled]:active,
fieldset[disabled] .btn-red:active,
.btn-red.disabled.active,
.btn-red[disabled].active,
fieldset[disabled] .btn-red.active {
  background-color: #d42020;
  border-color: #d42020;
}
.btn-red .badge {
  color: #d42020;
  background-color: #ffffff;
}
.btn-red > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-red.dropdown-toggle {
  border-left-color: #b51b1b;
}
.btn-red.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-red.btn-icon i {
  background-color: #b11b1b;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-red.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-red.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-red.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-red.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-red.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-red.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-red.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-red.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-red.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-red.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-red.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-orange {
  color: #ffffff;
  background-color: #ff9600;
  border-color: #ff9600;
}
.btn-orange:hover,
.btn-orange:focus,
.btn-orange:active,
.btn-orange.active,
.open .dropdown-toggle.btn-orange {
  color: #ffffff;
  background-color: #d67e00;
  border-color: #c27200;
}
.btn-orange:active,
.btn-orange.active,
.open .dropdown-toggle.btn-orange {
  background-image: none;
}
.btn-orange.disabled,
.btn-orange[disabled],
fieldset[disabled] .btn-orange,
.btn-orange.disabled:hover,
.btn-orange[disabled]:hover,
fieldset[disabled] .btn-orange:hover,
.btn-orange.disabled:focus,
.btn-orange[disabled]:focus,
fieldset[disabled] .btn-orange:focus,
.btn-orange.disabled:active,
.btn-orange[disabled]:active,
fieldset[disabled] .btn-orange:active,
.btn-orange.disabled.active,
.btn-orange[disabled].active,
fieldset[disabled] .btn-orange.active {
  background-color: #ff9600;
  border-color: #ff9600;
}
.btn-orange .badge {
  color: #ff9600;
  background-color: #ffffff;
}
.btn-orange > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-orange.dropdown-toggle {
  border-left-color: #db8100;
}
.btn-orange.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-orange.btn-icon i {
  background-color: #d67e00;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-orange.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-orange.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-orange.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-orange.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-orange.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-orange.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-orange.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-orange.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-orange.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-orange.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-orange.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-gold {
  color: #846e20;
  background-color: #fcd036;
  border-color: #fcd036;
}
.btn-gold:hover,
.btn-gold:focus,
.btn-gold:active,
.btn-gold.active,
.open .dropdown-toggle.btn-gold {
  color: #846e20;
  background-color: #fbc70e;
  border-color: #f1bc04;
}
.btn-gold:active,
.btn-gold.active,
.open .dropdown-toggle.btn-gold {
  background-image: none;
}
.btn-gold.disabled,
.btn-gold[disabled],
fieldset[disabled] .btn-gold,
.btn-gold.disabled:hover,
.btn-gold[disabled]:hover,
fieldset[disabled] .btn-gold:hover,
.btn-gold.disabled:focus,
.btn-gold[disabled]:focus,
fieldset[disabled] .btn-gold:focus,
.btn-gold.disabled:active,
.btn-gold[disabled]:active,
fieldset[disabled] .btn-gold:active,
.btn-gold.disabled.active,
.btn-gold[disabled].active,
fieldset[disabled] .btn-gold.active {
  background-color: #fcd036;
  border-color: #fcd036;
}
.btn-gold .badge {
  color: #fcd036;
  background-color: #846e20;
}
.btn-gold > .caret {
  border-top-color: #846e20;
  border-bottom-color: #846e20 !important;
}
.btn-gold.dropdown-toggle {
  border-left-color: #fbc813;
}
.btn-gold.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-gold.btn-icon i {
  background-color: #fbc70e;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-gold.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-gold.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-gold.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-gold.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-gold.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-gold.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-gold.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-gold.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-gold.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-gold.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-gold.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-black {
  color: #ffffff;
  background-color: #000000;
  border-color: #000000;
}
.btn-black:hover,
.btn-black:focus,
.btn-black:active,
.btn-black.active,
.open .dropdown-toggle.btn-black {
  color: #ffffff;
  background-color: #000000;
  border-color: #000000;
}
.btn-black:active,
.btn-black.active,
.open .dropdown-toggle.btn-black {
  background-image: none;
}
.btn-black.disabled,
.btn-black[disabled],
fieldset[disabled] .btn-black,
.btn-black.disabled:hover,
.btn-black[disabled]:hover,
fieldset[disabled] .btn-black:hover,
.btn-black.disabled:focus,
.btn-black[disabled]:focus,
fieldset[disabled] .btn-black:focus,
.btn-black.disabled:active,
.btn-black[disabled]:active,
fieldset[disabled] .btn-black:active,
.btn-black.disabled.active,
.btn-black[disabled].active,
fieldset[disabled] .btn-black.active {
  background-color: #000000;
  border-color: #000000;
}
.btn-black .badge {
  color: #000000;
  background-color: #ffffff;
}
.btn-black > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-black.dropdown-toggle {
  border-left-color: #000000;
}
.btn-black.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-black.btn-icon i {
  background-color: #000000;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-black.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-black.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-black.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-black.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-black.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-black.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-black.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-black.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-black.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-black.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-black.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-white {
  color: #303641;
  background-color: #ffffff;
  border-color: #ffffff;
  border-color: #ebebeb;
}
.btn-white:hover,
.btn-white:focus,
.btn-white:active,
.btn-white.active,
.open .dropdown-toggle.btn-white {
  color: #303641;
  background-color: #ebebeb;
  border-color: #e0e0e0;
}
.btn-white:active,
.btn-white.active,
.open .dropdown-toggle.btn-white {
  background-image: none;
}
.btn-white.disabled,
.btn-white[disabled],
fieldset[disabled] .btn-white,
.btn-white.disabled:hover,
.btn-white[disabled]:hover,
fieldset[disabled] .btn-white:hover,
.btn-white.disabled:focus,
.btn-white[disabled]:focus,
fieldset[disabled] .btn-white:focus,
.btn-white.disabled:active,
.btn-white[disabled]:active,
fieldset[disabled] .btn-white:active,
.btn-white.disabled.active,
.btn-white[disabled].active,
fieldset[disabled] .btn-white.active {
  background-color: #ffffff;
  border-color: #ffffff;
}
.btn-white .badge {
  color: #ffffff;
  background-color: #303641;
}
.btn-white > .caret {
  border-top-color: #303641;
  border-bottom-color: #303641 !important;
}
.btn-white.dropdown-toggle {
  border-left-color: #ededed;
}
.btn-white.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-white.btn-icon i {
  background-color: #ebebeb;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-white.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-white.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-white.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-white.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-white.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-white.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-white.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-white.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-white.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-white.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-white.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-warning {
  color: #ffffff;
  background-color: #fad839;
  border-color: #fad839;
}
.btn-warning:hover,
.btn-warning:focus,
.btn-warning:active,
.btn-warning.active,
.open .dropdown-toggle.btn-warning {
  color: #ffffff;
  background-color: #f9d011;
  border-color: #f0c706;
}
.btn-warning:active,
.btn-warning.active,
.open .dropdown-toggle.btn-warning {
  background-image: none;
}
.btn-warning.disabled,
.btn-warning[disabled],
fieldset[disabled] .btn-warning,
.btn-warning.disabled:hover,
.btn-warning[disabled]:hover,
fieldset[disabled] .btn-warning:hover,
.btn-warning.disabled:focus,
.btn-warning[disabled]:focus,
fieldset[disabled] .btn-warning:focus,
.btn-warning.disabled:active,
.btn-warning[disabled]:active,
fieldset[disabled] .btn-warning:active,
.btn-warning.disabled.active,
.btn-warning[disabled].active,
fieldset[disabled] .btn-warning.active {
  background-color: #fad839;
  border-color: #fad839;
}
.btn-warning .badge {
  color: #fad839;
  background-color: #ffffff;
}
.btn-warning > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-warning.dropdown-toggle {
  border-left-color: #f9d116;
}
.btn-warning.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-warning.btn-icon i {
  background-color: #f9d011;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-warning.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-warning.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-warning.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-warning.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-warning.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-warning.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-warning.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-warning.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-warning.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-warning.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-warning.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-danger {
  color: #ffffff;
  background-color: #cc2424;
  border-color: #cc2424;
}
.btn-danger:hover,
.btn-danger:focus,
.btn-danger:active,
.btn-danger.active,
.open .dropdown-toggle.btn-danger {
  color: #ffffff;
  background-color: #a91e1e;
  border-color: #981b1b;
}
.btn-danger:active,
.btn-danger.active,
.open .dropdown-toggle.btn-danger {
  background-image: none;
}
.btn-danger.disabled,
.btn-danger[disabled],
fieldset[disabled] .btn-danger,
.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled:active,
.btn-danger[disabled]:active,
fieldset[disabled] .btn-danger:active,
.btn-danger.disabled.active,
.btn-danger[disabled].active,
fieldset[disabled] .btn-danger.active {
  background-color: #cc2424;
  border-color: #cc2424;
}
.btn-danger .badge {
  color: #cc2424;
  background-color: #ffffff;
}
.btn-danger > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-danger.dropdown-toggle {
  border-left-color: #ae1f1f;
}
.btn-danger.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-danger.btn-icon i {
  background-color: #a91e1e;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-danger.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-danger.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-danger.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-danger.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-danger.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-danger.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-danger.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-danger.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-danger.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-danger.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-danger.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-success,
.btn-green {
  color: #ffffff;
  background-color: #00a651;
  border-color: #00a651;
}
.btn-success:hover,
.btn-green:hover,
.btn-success:focus,
.btn-green:focus,
.btn-success:active,
.btn-green:active,
.btn-success.active,
.btn-green.active,
.open .dropdown-toggle.btn-success,
.open .dropdown-toggle.btn-green {
  color: #ffffff;
  background-color: #007d3d;
  border-color: #006933;
}
.btn-success:active,
.btn-green:active,
.btn-success.active,
.btn-green.active,
.open .dropdown-toggle.btn-success,
.open .dropdown-toggle.btn-green {
  background-image: none;
}
.btn-success.disabled,
.btn-green.disabled,
.btn-success[disabled],
.btn-green[disabled],
fieldset[disabled] .btn-success,
fieldset[disabled] .btn-green,
.btn-success.disabled:hover,
.btn-green.disabled:hover,
.btn-success[disabled]:hover,
.btn-green[disabled]:hover,
fieldset[disabled] .btn-success:hover,
fieldset[disabled] .btn-green:hover,
.btn-success.disabled:focus,
.btn-green.disabled:focus,
.btn-success[disabled]:focus,
.btn-green[disabled]:focus,
fieldset[disabled] .btn-success:focus,
fieldset[disabled] .btn-green:focus,
.btn-success.disabled:active,
.btn-green.disabled:active,
.btn-success[disabled]:active,
.btn-green[disabled]:active,
fieldset[disabled] .btn-success:active,
fieldset[disabled] .btn-green:active,
.btn-success.disabled.active,
.btn-green.disabled.active,
.btn-success[disabled].active,
.btn-green[disabled].active,
fieldset[disabled] .btn-success.active,
fieldset[disabled] .btn-green.active {
  background-color: #00a651;
  border-color: #00a651;
}
.btn-success .badge,
.btn-green .badge {
  color: #00a651;
  background-color: #ffffff;
}
.btn-success > .caret,
.btn-green > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-success.dropdown-toggle,
.btn-green.dropdown-toggle {
  border-left-color: #008240;
}
.btn-success.btn-icon,
.btn-green.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-success.btn-icon i,
.btn-green.btn-icon i {
  background-color: #007d3d;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-success.btn-icon.icon-left,
.btn-green.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-success.btn-icon.icon-left i,
.btn-green.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-success.btn-icon.btn-lg,
.btn-green.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-success.btn-icon.btn-lg.icon-left,
.btn-green.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-success.btn-icon.btn-lg i,
.btn-green.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-success.btn-icon.btn-sm,
.btn-green.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-success.btn-icon.btn-sm.icon-left,
.btn-green.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-success.btn-icon.btn-sm i,
.btn-green.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-success.btn-icon.btn-xs,
.btn-green.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-success.btn-icon.btn-xs.icon-left,
.btn-green.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-success.btn-icon.btn-xs i,
.btn-green.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-info {
  color: #ffffff;
  background-color: #21a9e1;
  border-color: #21a9e1;
}
.btn-info:hover,
.btn-info:focus,
.btn-info:active,
.btn-info.active,
.open .dropdown-toggle.btn-info {
  color: #ffffff;
  background-color: #1a8fbf;
  border-color: #1782ad;
}
.btn-info:active,
.btn-info.active,
.open .dropdown-toggle.btn-info {
  background-image: none;
}
.btn-info.disabled,
.btn-info[disabled],
fieldset[disabled] .btn-info,
.btn-info.disabled:hover,
.btn-info[disabled]:hover,
fieldset[disabled] .btn-info:hover,
.btn-info.disabled:focus,
.btn-info[disabled]:focus,
fieldset[disabled] .btn-info:focus,
.btn-info.disabled:active,
.btn-info[disabled]:active,
fieldset[disabled] .btn-info:active,
.btn-info.disabled.active,
.btn-info[disabled].active,
fieldset[disabled] .btn-info.active {
  background-color: #21a9e1;
  border-color: #21a9e1;
}
.btn-info .badge {
  color: #21a9e1;
  background-color: #ffffff;
}
.btn-info > .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff !important;
}
.btn-info.dropdown-toggle {
  border-left-color: #1a92c4;
}
.btn-info.btn-icon {
  position: relative;
  padding-right: 39px;
  border: none;
}
.btn-info.btn-icon i {
  background-color: #1a8fbf;
  padding: 6px 6px;
  font-size: 12px;
  line-height: 1.42857143;
  border-radius: 3px;
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.btn-info.btn-icon.icon-left {
  padding-right: 12px;
  padding-left: 39px;
}
.btn-info.btn-icon.icon-left i {
  float: left;
  right: auto;
  left: 0;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
}
.btn-info.btn-icon.btn-lg {
  padding-right: 55px;
}
.btn-info.btn-icon.btn-lg.icon-left {
  padding-right: 16px;
  padding-left: 55px;
}
.btn-info.btn-icon.btn-lg i {
  padding: 10px 10px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-info.btn-icon.btn-sm {
  padding-right: 36px;
}
.btn-info.btn-icon.btn-sm.icon-left {
  padding-right: 10px;
  padding-left: 36px;
}
.btn-info.btn-icon.btn-sm i {
  padding: 5px 6px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-info.btn-icon.btn-xs {
  padding-right: 32px;
}
.btn-info.btn-icon.btn-xs.icon-left {
  padding-right: 10px;
  padding-left: 32px;
}
.btn-info.btn-icon.btn-xs i {
  padding: 2px 6px;
  font-size: 10px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-link {
  color: #373e4a;
  font-weight: normal;
  cursor: pointer;
  border-radius: 0;
}
.btn-link,
.btn-link:active,
.btn-link[disabled],
fieldset[disabled] .btn-link {
  background-color: transparent;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn-link,
.btn-link:hover,
.btn-link:focus,
.btn-link:active {
  border-color: transparent;
}
.btn-link:hover,
.btn-link:focus {
  color: #818da2;
  text-decoration: underline;
  background-color: transparent;
}
.btn-link[disabled]:hover,
fieldset[disabled] .btn-link:hover,
.btn-link[disabled]:focus,
fieldset[disabled] .btn-link:focus {
  color: #999999;
  text-decoration: none;
}
.btn-lg {
  padding: 10px 16px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-sm,
.btn-xs {
  padding: 5px 10px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-xs {
  padding: 1px 5px;
}
.btn-block {
  display: block;
  width: 100%;
  padding-left: 0;
  padding-right: 0;
}
.btn-block + .btn-block {
  margin-top: 5px;
}
input[type="submit"].btn-block,
input[type="reset"].btn-block,
input[type="button"].btn-block {
  width: 100%;
}
.fade {
  opacity: 0;
  -moz-transition: opacity 0.15s linear;
  -o-transition: opacity 0.15s linear;
  -webkit-transition: opacity 0.15s linear;
  transition: opacity 0.15s linear;
}
.fade.in {
  opacity: 1;
}
.collapse {
  display: none;
}
.collapse.in {
  display: block;
}
.collapsing {
  position: relative;
  height: 0;
  overflow: hidden;
  -moz-transition: height 0.35s ease;
  -o-transition: height 0.35s ease;
  -webkit-transition: height 0.35s ease;
  transition: height 0.35s ease;
}
@font-face {
  font-family: 'Glyphicons Halflings';
  src: url('../font/glyphicons-halflings-regular.eot');
  src: url('../font/glyphicons-halflings-regular.eot#iefix') format('embedded-opentype'), url('../font/glyphicons-halflings-regular.woff') format('woff'), url('../font/glyphicons-halflings-regular.ttf') format('truetype'), url('http://view.jqueryfuns.com/2014/6/3/829_b660c4cdfa003f1fe552d5969ad22374/assets/fonts/glyphicons-halflings-regular.svg#glyphicons_halflingsregular') format('svg');
}
.glyphicon {
  position: relative;
  top: 1px;
  display: inline-block;
  font-family: 'Glyphicons Halflings';
  font-style: normal;
  font-weight: normal;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.glyphicon:empty {
  width: 1em;
}
.glyphicon-asterisk:before {
  content: "\2a";
}
.glyphicon-plus:before {
  content: "\2b";
}
.glyphicon-euro:before {
  content: "\20ac";
}
.glyphicon-minus:before {
  content: "\2212";
}
.glyphicon-cloud:before {
  content: "\2601";
}
.glyphicon-envelope:before {
  content: "\2709";
}
.glyphicon-pencil:before {
  content: "\270f";
}
.glyphicon-glass:before {
  content: "\e001";
}
.glyphicon-music:before {
  content: "\e002";
}
.glyphicon-search:before {
  content: "\e003";
}
.glyphicon-heart:before {
  content: "\e005";
}
.glyphicon-star:before {
  content: "\e006";
}
.glyphicon-star-empty:before {
  content: "\e007";
}
.glyphicon-user:before {
  content: "\e008";
}
.glyphicon-film:before {
  content: "\e009";
}
.glyphicon-th-large:before {
  content: "\e010";
}
.glyphicon-th:before {
  content: "\e011";
}
.glyphicon-th-list:before {
  content: "\e012";
}
.glyphicon-ok:before {
  content: "\e013";
}
.glyphicon-remove:before {
  content: "\e014";
}
.glyphicon-zoom-in:before {
  content: "\e015";
}
.glyphicon-zoom-out:before {
  content: "\e016";
}
.glyphicon-off:before {
  content: "\e017";
}
.glyphicon-signal:before {
  content: "\e018";
}
.glyphicon-cog:before {
  content: "\e019";
}
.glyphicon-trash:before {
  content: "\e020";
}
.glyphicon-home:before {
  content: "\e021";
}
.glyphicon-file:before {
  content: "\e022";
}
.glyphicon-time:before {
  content: "\e023";
}
.glyphicon-road:before {
  content: "\e024";
}
.glyphicon-download-alt:before {
  content: "\e025";
}
.glyphicon-download:before {
  content: "\e026";
}
.glyphicon-upload:before {
  content: "\e027";
}
.glyphicon-inbox:before {
  content: "\e028";
}
.glyphicon-play-circle:before {
  content: "\e029";
}
.glyphicon-repeat:before {
  content: "\e030";
}
.glyphicon-refresh:before {
  content: "\e031";
}
.glyphicon-list-alt:before {
  content: "\e032";
}
.glyphicon-lock:before {
  content: "\e033";
}
.glyphicon-flag:before {
  content: "\e034";
}
.glyphicon-headphones:before {
  content: "\e035";
}
.glyphicon-volume-off:before {
  content: "\e036";
}
.glyphicon-volume-down:before {
  content: "\e037";
}
.glyphicon-volume-up:before {
  content: "\e038";
}
.glyphicon-qrcode:before {
  content: "\e039";
}
.glyphicon-barcode:before {
  content: "\e040";
}
.glyphicon-tag:before {
  content: "\e041";
}
.glyphicon-tags:before {
  content: "\e042";
}
.glyphicon-book:before {
  content: "\e043";
}
.glyphicon-bookmark:before {
  content: "\e044";
}
.glyphicon-print:before {
  content: "\e045";
}
.glyphicon-camera:before {
  content: "\e046";
}
.glyphicon-font:before {
  content: "\e047";
}
.glyphicon-bold:before {
  content: "\e048";
}
.glyphicon-italic:before {
  content: "\e049";
}
.glyphicon-text-height:before {
  content: "\e050";
}
.glyphicon-text-width:before {
  content: "\e051";
}
.glyphicon-align-left:before {
  content: "\e052";
}
.glyphicon-align-center:before {
  content: "\e053";
}
.glyphicon-align-right:before {
  content: "\e054";
}
.glyphicon-align-justify:before {
  content: "\e055";
}
.glyphicon-list:before {
  content: "\e056";
}
.glyphicon-indent-left:before {
  content: "\e057";
}
.glyphicon-indent-right:before {
  content: "\e058";
}
.glyphicon-facetime-video:before {
  content: "\e059";
}
.glyphicon-picture:before {
  content: "\e060";
}
.glyphicon-map-marker:before {
  content: "\e062";
}
.glyphicon-adjust:before {
  content: "\e063";
}
.glyphicon-tint:before {
  content: "\e064";
}
.glyphicon-edit:before {
  content: "\e065";
}
.glyphicon-share:before {
  content: "\e066";
}
.glyphicon-check:before {
  content: "\e067";
}
.glyphicon-move:before {
  content: "\e068";
}
.glyphicon-step-backward:before {
  content: "\e069";
}
.glyphicon-fast-backward:before {
  content: "\e070";
}
.glyphicon-backward:before {
  content: "\e071";
}
.glyphicon-play:before {
  content: "\e072";
}
.glyphicon-pause:before {
  content: "\e073";
}
.glyphicon-stop:before {
  content: "\e074";
}
.glyphicon-forward:before {
  content: "\e075";
}
.glyphicon-fast-forward:before {
  content: "\e076";
}
.glyphicon-step-forward:before {
  content: "\e077";
}
.glyphicon-eject:before {
  content: "\e078";
}
.glyphicon-chevron-left:before {
  content: "\e079";
}
.glyphicon-chevron-right:before {
  content: "\e080";
}
.glyphicon-plus-sign:before {
  content: "\e081";
}
.glyphicon-minus-sign:before {
  content: "\e082";
}
.glyphicon-remove-sign:before {
  content: "\e083";
}
.glyphicon-ok-sign:before {
  content: "\e084";
}
.glyphicon-question-sign:before {
  content: "\e085";
}
.glyphicon-info-sign:before {
  content: "\e086";
}
.glyphicon-screenshot:before {
  content: "\e087";
}
.glyphicon-remove-circle:before {
  content: "\e088";
}
.glyphicon-ok-circle:before {
  content: "\e089";
}
.glyphicon-ban-circle:before {
  content: "\e090";
}
.glyphicon-arrow-left:before {
  content: "\e091";
}
.glyphicon-arrow-right:before {
  content: "\e092";
}
.glyphicon-arrow-up:before {
  content: "\e093";
}
.glyphicon-arrow-down:before {
  content: "\e094";
}
.glyphicon-share-alt:before {
  content: "\e095";
}
.glyphicon-resize-full:before {
  content: "\e096";
}
.glyphicon-resize-small:before {
  content: "\e097";
}
.glyphicon-exclamation-sign:before {
  content: "\e101";
}
.glyphicon-gift:before {
  content: "\e102";
}
.glyphicon-leaf:before {
  content: "\e103";
}
.glyphicon-fire:before {
  content: "\e104";
}
.glyphicon-eye-open:before {
  content: "\e105";
}
.glyphicon-eye-close:before {
  content: "\e106";
}
.glyphicon-warning-sign:before {
  content: "\e107";
}
.glyphicon-plane:before {
  content: "\e108";
}
.glyphicon-calendar:before {
  content: "\e109";
}
.glyphicon-random:before {
  content: "\e110";
}
.glyphicon-comment:before {
  content: "\e111";
}
.glyphicon-magnet:before {
  content: "\e112";
}
.glyphicon-chevron-up:before {
  content: "\e113";
}
.glyphicon-chevron-down:before {
  content: "\e114";
}
.glyphicon-retweet:before {
  content: "\e115";
}
.glyphicon-shopping-cart:before {
  content: "\e116";
}
.glyphicon-folder-close:before {
  content: "\e117";
}
.glyphicon-folder-open:before {
  content: "\e118";
}
.glyphicon-resize-vertical:before {
  content: "\e119";
}
.glyphicon-resize-horizontal:before {
  content: "\e120";
}
.glyphicon-hdd:before {
  content: "\e121";
}
.glyphicon-bullhorn:before {
  content: "\e122";
}
.glyphicon-bell:before {
  content: "\e123";
}
.glyphicon-certificate:before {
  content: "\e124";
}
.glyphicon-thumbs-up:before {
  content: "\e125";
}
.glyphicon-thumbs-down:before {
  content: "\e126";
}
.glyphicon-hand-right:before {
  content: "\e127";
}
.glyphicon-hand-left:before {
  content: "\e128";
}
.glyphicon-hand-up:before {
  content: "\e129";
}
.glyphicon-hand-down:before {
  content: "\e130";
}
.glyphicon-circle-arrow-right:before {
  content: "\e131";
}
.glyphicon-circle-arrow-left:before {
  content: "\e132";
}
.glyphicon-circle-arrow-up:before {
  content: "\e133";
}
.glyphicon-circle-arrow-down:before {
  content: "\e134";
}
.glyphicon-globe:before {
  content: "\e135";
}
.glyphicon-wrench:before {
  content: "\e136";
}
.glyphicon-tasks:before {
  content: "\e137";
}
.glyphicon-filter:before {
  content: "\e138";
}
.glyphicon-briefcase:before {
  content: "\e139";
}
.glyphicon-fullscreen:before {
  content: "\e140";
}
.glyphicon-dashboard:before {
  content: "\e141";
}
.glyphicon-paperclip:before {
  content: "\e142";
}
.glyphicon-heart-empty:before {
  content: "\e143";
}
.glyphicon-link:before {
  content: "\e144";
}
.glyphicon-phone:before {
  content: "\e145";
}
.glyphicon-pushpin:before {
  content: "\e146";
}
.glyphicon-usd:before {
  content: "\e148";
}
.glyphicon-gbp:before {
  content: "\e149";
}
.glyphicon-sort:before {
  content: "\e150";
}
.glyphicon-sort-by-alphabet:before {
  content: "\e151";
}
.glyphicon-sort-by-alphabet-alt:before {
  content: "\e152";
}
.glyphicon-sort-by-order:before {
  content: "\e153";
}
.glyphicon-sort-by-order-alt:before {
  content: "\e154";
}
.glyphicon-sort-by-attributes:before {
  content: "\e155";
}
.glyphicon-sort-by-attributes-alt:before {
  content: "\e156";
}
.glyphicon-unchecked:before {
  content: "\e157";
}
.glyphicon-expand:before {
  content: "\e158";
}
.glyphicon-collapse-down:before {
  content: "\e159";
}
.glyphicon-collapse-up:before {
  content: "\e160";
}
.glyphicon-log-in:before {
  content: "\e161";
}
.glyphicon-flash:before {
  content: "\e162";
}
.glyphicon-log-out:before {
  content: "\e163";
}
.glyphicon-new-window:before {
  content: "\e164";
}
.glyphicon-record:before {
  content: "\e165";
}
.glyphicon-save:before {
  content: "\e166";
}
.glyphicon-open:before {
  content: "\e167";
}
.glyphicon-saved:before {
  content: "\e168";
}
.glyphicon-import:before {
  content: "\e169";
}
.glyphicon-export:before {
  content: "\e170";
}
.glyphicon-send:before {
  content: "\e171";
}
.glyphicon-floppy-disk:before {
  content: "\e172";
}
.glyphicon-floppy-saved:before {
  content: "\e173";
}
.glyphicon-floppy-remove:before {
  content: "\e174";
}
.glyphicon-floppy-save:before {
  content: "\e175";
}
.glyphicon-floppy-open:before {
  content: "\e176";
}
.glyphicon-credit-card:before {
  content: "\e177";
}
.glyphicon-transfer:before {
  content: "\e178";
}
.glyphicon-cutlery:before {
  content: "\e179";
}
.glyphicon-header:before {
  content: "\e180";
}
.glyphicon-compressed:before {
  content: "\e181";
}
.glyphicon-earphone:before {
  content: "\e182";
}
.glyphicon-phone-alt:before {
  content: "\e183";
}
.glyphicon-tower:before {
  content: "\e184";
}
.glyphicon-stats:before {
  content: "\e185";
}
.glyphicon-sd-video:before {
  content: "\e186";
}
.glyphicon-hd-video:before {
  content: "\e187";
}
.glyphicon-subtitles:before {
  content: "\e188";
}
.glyphicon-sound-stereo:before {
  content: "\e189";
}
.glyphicon-sound-dolby:before {
  content: "\e190";
}
.glyphicon-sound-5-1:before {
  content: "\e191";
}
.glyphicon-sound-6-1:before {
  content: "\e192";
}
.glyphicon-sound-7-1:before {
  content: "\e193";
}
.glyphicon-copyright-mark:before {
  content: "\e194";
}
.glyphicon-registration-mark:before {
  content: "\e195";
}
.glyphicon-cloud-download:before {
  content: "\e197";
}
.glyphicon-cloud-upload:before {
  content: "\e198";
}
.glyphicon-tree-conifer:before {
  content: "\e199";
}
.glyphicon-tree-deciduous:before {
  content: "\e200";
}
.caret {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 2px;
  vertical-align: middle;
  border-top: 4px solid #000000;
  border-right: 4px solid transparent;
  border-left: 4px solid transparent;
  border-bottom: 0 dotted;
}
.dropdown {
  position: relative;
}
.dropdown-toggle:focus {
  outline: 0;
}
.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1000;
  display: none;
  float: left;
  min-width: 160px;
  padding: 3px 0;
  margin: 2px 0 0;
  list-style: none;
  font-size: 12px;
  background-color: #ffffff;
  border: 1px solid #cccccc;
  border: 1px solid #ebebeb;
  border-radius: 3px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  background-clip: padding-box;
}
.dropdown-menu.pull-right {
  right: 0;
  left: auto;
}
.dropdown-menu .divider {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #eeeeee;
}
.dropdown-menu > li > a {
  display: block;
  padding: 4px 8px;
  clear: both;
  font-weight: normal;
  line-height: 1.42857143;
  color: #7d8086;
  margin: 0 3px;
  white-space: nowrap;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.dropdown-menu.dropdown-green {
  background-color: #00a651;
  border-color: #009247;
}
.dropdown-menu.dropdown-green > li > a {
  color: #ffffff;
}
.dropdown-menu.dropdown-green > li:hover a {
  background-color: #008d45;
  color: #ffffff;
}
.dropdown-menu.dropdown-green .divider,
.dropdown-menu.dropdown-green .divider:hover {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #009247;
}
.dropdown-menu.dropdown-gold {
  background-color: #fcd036;
  border-color: #ecb804;
}
.dropdown-menu.dropdown-gold > li > a {
  color: #846e20;
}
.dropdown-menu.dropdown-gold > li:hover a {
  background-color: #f1bc04;
  color: #846e20;
}
.dropdown-menu.dropdown-gold .divider,
.dropdown-menu.dropdown-gold .divider:hover {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #ecb804;
}
.dropdown-menu.dropdown-blue {
  background-color: #0072bc;
  border-color: #0063a3;
}
.dropdown-menu.dropdown-blue > li > a {
  color: #ffffff;
}
.dropdown-menu.dropdown-blue > li:hover a {
  background-color: #0063a3;
  color: #ffffff;
}
.dropdown-menu.dropdown-blue .divider,
.dropdown-menu.dropdown-blue .divider:hover {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #0063a3;
}
.dropdown-menu.dropdown-primary {
  background-color: #303641;
  border-color: #454a54;
}
.dropdown-menu.dropdown-primary > li > a {
  color: #ffffff;
}
.dropdown-menu.dropdown-primary > li:hover a {
  background-color: #252a32;
  color: #ffffff;
}
.dropdown-menu.dropdown-primary .divider,
.dropdown-menu.dropdown-primary .divider:hover {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #454a54;
}
.dropdown-menu.dropdown-red {
  background-color: #d42020;
  border-color: #b51b1b;
}
.dropdown-menu.dropdown-red > li > a {
  color: #ffffff;
}
.dropdown-menu.dropdown-red > li:hover a {
  background-color: #be1d1d;
  color: #ffffff;
}
.dropdown-menu.dropdown-red .divider,
.dropdown-menu.dropdown-red .divider:hover {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #b51b1b;
}
.dropdown-menu.dropdown-black {
  background-color: #000000;
  border-color: #454545;
}
.dropdown-menu.dropdown-black > li > a {
  color: #ffffff;
}
.dropdown-menu.dropdown-black > li:hover a {
  background-color: #383838;
  color: #ffffff;
}
.dropdown-menu.dropdown-black .divider,
.dropdown-menu.dropdown-black .divider:hover {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #454545;
}
.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus {
  text-decoration: none;
  color: #717379;
  background-color: #f5f5f5;
}
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus {
  color: #ffffff;
  text-decoration: none;
  outline: 0;
  background-color: #949494;
}
.dropdown-menu > .disabled > a,
.dropdown-menu > .disabled > a:hover,
.dropdown-menu > .disabled > a:focus {
  color: #999999;
}
.dropdown-menu > .disabled > a:hover,
.dropdown-menu > .disabled > a:focus {
  text-decoration: none;
  background-color: transparent;
  background-image: none;
  filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
  cursor: not-allowed;
}
.open > .dropdown-menu {
  display: block;
}
.open > a {
  outline: 0;
}
.dropdown-header {
  display: block;
  padding: 3px 20px;
  font-size: 11px;
  line-height: 1.42857143;
  color: #999999;
}
.dropdown-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 990;
}
.pull-right > .dropdown-menu {
  right: 0;
  left: auto;
}
.dropup .caret,
.navbar-fixed-bottom .dropdown .caret {
  border-top: 0 dotted;
  border-bottom: 4px solid #000000;
  content: "";
}
.dropup .dropdown-menu,
.navbar-fixed-bottom .dropdown .dropdown-menu {
  top: auto;
  bottom: 100%;
  margin-bottom: 1px;
}
@media (min-width: 768px) {
  .navbar-right .dropdown-menu {
    right: 0;
    left: auto;
  }
}
.btn-default .caret {
  border-top-color: #303641;
}
.btn-primary .caret,
.btn-success .caret,
.btn-warning .caret,
.btn-danger .caret,
.btn-info .caret {
  border-top-color: #fff;
}
.dropup .btn-default .caret {
  border-bottom-color: #303641;
}
.dropup .btn-primary .caret,
.dropup .btn-success .caret,
.dropup .btn-warning .caret,
.dropup .btn-danger .caret,
.dropup .btn-info .caret {
  border-bottom-color: #fff;
}
.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}
.btn-group:before,
.btn-group-vertical:before,
.btn-group:after,
.btn-group-vertical:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.btn-group:after,
.btn-group-vertical:after {
  clear: both;
}
.btn-group:before,
.btn-group-vertical:before,
.btn-group:after,
.btn-group-vertical:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.btn-group:after,
.btn-group-vertical:after {
  clear: both;
}
.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  float: left;
}
.btn-group > .btn:hover,
.btn-group-vertical > .btn:hover,
.btn-group > .btn:focus,
.btn-group-vertical > .btn:focus,
.btn-group > .btn:active,
.btn-group-vertical > .btn:active,
.btn-group > .btn.active,
.btn-group-vertical > .btn.active {
  z-index: 2;
}
.btn-group > .btn:focus,
.btn-group-vertical > .btn:focus {
  outline: none;
}
.btn-group.left-dropdown .btn {
  float: right;
  -webkit-border-radius: 0 3px 3px 0 !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0 !important;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0 !important;
  background-clip: padding-box;
}
.btn-group.left-dropdown .btn.dropdown-toggle {
  float: left;
  -webkit-border-radius: 3px 0 0 3px !important;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px !important;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px !important;
  background-clip: padding-box;
  border-left: 0;
}
.btn-group .btn + .btn,
.btn-group .btn + .btn-group,
.btn-group .btn-group + .btn,
.btn-group .btn-group + .btn-group {
  margin-left: -1px;
}
.btn-toolbar:before,
.btn-toolbar:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.btn-toolbar:after {
  clear: both;
}
.btn-toolbar:before,
.btn-toolbar:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.btn-toolbar:after {
  clear: both;
}
.btn-toolbar .btn-group {
  float: left;
}
.btn-toolbar > .btn + .btn,
.btn-toolbar > .btn-group + .btn,
.btn-toolbar > .btn + .btn-group,
.btn-toolbar > .btn-group + .btn-group {
  margin-left: 5px;
}
.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0;
}
.btn-group > .btn:first-child {
  margin-left: 0;
}
.btn-group > .btn:first-child:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group > .btn:last-child:not(:first-child),
.btn-group > .dropdown-toggle:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.btn-group > .btn-group {
  float: left;
}
.btn-group > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group > .btn-group:first-child > .btn:last-child,
.btn-group > .btn-group:first-child > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.btn-group > .btn-group:last-child > .btn:first-child {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.btn-group .dropdown-toggle:active,
.btn-group.open .dropdown-toggle {
  outline: 0;
}
.btn-group-xs > .btn {
  padding: 5px 10px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
  padding: 1px 5px;
}
.btn-group-sm > .btn {
  padding: 5px 10px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
.btn-group-lg > .btn {
  padding: 10px 16px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
.btn-group > .btn + .dropdown-toggle {
  padding-left: 8px;
  padding-right: 8px;
}
.btn-group > .btn-lg + .dropdown-toggle {
  padding-left: 12px;
  padding-right: 12px;
}
.btn-group.open .dropdown-toggle {
  -moz-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}
.btn-group.open .dropdown-toggle.btn-link {
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.btn .caret {
  margin-left: 0;
}
.btn-lg .caret {
  border-width: 5px 5px 0;
  border-bottom-width: 0;
}
.dropup .btn-lg .caret {
  border-width: 0 5px 5px;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  display: block;
  float: none;
  width: 100%;
  max-width: 100%;
}
.btn-group-vertical > .btn-group:before,
.btn-group-vertical > .btn-group:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.btn-group-vertical > .btn-group:after {
  clear: both;
}
.btn-group-vertical > .btn-group:before,
.btn-group-vertical > .btn-group:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.btn-group-vertical > .btn-group:after {
  clear: both;
}
.btn-group-vertical > .btn-group > .btn {
  float: none;
}
.btn-group-vertical > .btn + .btn,
.btn-group-vertical > .btn + .btn-group,
.btn-group-vertical > .btn-group + .btn,
.btn-group-vertical > .btn-group + .btn-group {
  margin-top: -1px;
  margin-left: 0;
}
.btn-group-vertical > .btn:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.btn-group-vertical > .btn:first-child:not(:last-child) {
  border-top-right-radius: 3px;
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn:last-child:not(:first-child) {
  border-bottom-left-radius: 3px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.btn-group-vertical > .btn-group:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}
.btn-group-vertical > .btn-group:first-child > .btn:last-child,
.btn-group-vertical > .btn-group:first-child > .dropdown-toggle {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn-group:last-child > .btn:first-child {
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.btn-group-justified {
  display: table;
  width: 100%;
  table-layout: fixed;
  border-collapse: separate;
}
.btn-group-justified .btn {
  float: none;
  display: table-cell;
  width: 1%;
}
[data-toggle="buttons"] > .btn > input[type="radio"],
[data-toggle="buttons"] > .btn > input[type="checkbox"] {
  display: none;
}
.input-group {
  position: relative;
  display: table;
  border-collapse: separate;
}
.input-group.col {
  float: none;
  padding-left: 0;
  padding-right: 0;
}
.input-group .form-control {
  float: left;
  width: 100%;
  margin-bottom: 0;
}
.input-group.minimal .input-group-addon {
  background-color: transparent;
  padding-right: 0;
}
.input-group.minimal .input-group-addon + .form-control {
  border-left: 0;
  border-right-width: 1px;
}
.input-group.minimal > .input-group-addon:first-child {
  padding-left: 8px;
}
.input-group.minimal > .input-group-addon:first-child + .form-control {
  padding-left: 5px;
}
.input-group.minimal .form-control + .input-group-addon {
  padding-right: 12px;
  padding-left: 0;
}
.input-group.minimal .form-control {
  border-right-width: 0;
  padding-right: 5px;
}
.input-group.minimal .form-control + .input-group-addon {
  padding-right: 8px;
}
.input-group.minimal .form-control:focus {
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  border-color: #ebebeb;
}
.input-group-lg > .form-control,
.input-group-lg > .input-group-addon,
.input-group-lg > .input-group-btn > .btn {
  height: 41px;
  padding: 10px 16px;
  font-size: 15px;
  line-height: 1.33;
  border-radius: 3px;
}
select.input-group-lg > .form-control,
select.input-group-lg > .input-group-addon,
select.input-group-lg > .input-group-btn > .btn {
  height: 41px;
  line-height: 41px;
}
textarea.input-group-lg > .form-control,
textarea.input-group-lg > .input-group-addon,
textarea.input-group-lg > .input-group-btn > .btn,
select[multiple].input-group-lg > .form-control,
select[multiple].input-group-lg > .input-group-addon,
select[multiple].input-group-lg > .input-group-btn > .btn {
  height: auto;
}
.input-group-sm > .form-control,
.input-group-sm > .input-group-addon,
.input-group-sm > .input-group-btn > .btn {
  height: 28px;
  padding: 5px 10px;
  font-size: 11px;
  line-height: 1.5;
  border-radius: 2px;
}
select.input-group-sm > .form-control,
select.input-group-sm > .input-group-addon,
select.input-group-sm > .input-group-btn > .btn {
  height: 28px;
  line-height: 28px;
}
textarea.input-group-sm > .form-control,
textarea.input-group-sm > .input-group-addon,
textarea.input-group-sm > .input-group-btn > .btn,
select[multiple].input-group-sm > .form-control,
select[multiple].input-group-sm > .input-group-addon,
select[multiple].input-group-sm > .input-group-btn > .btn {
  height: auto;
}
.input-group-addon,
.input-group-btn,
.input-group .form-control {
  display: table-cell;
}
.input-group-addon:not(:first-child):not(:last-child),
.input-group-btn:not(:first-child):not(:last-child),
.input-group .form-control:not(:first-child):not(:last-child) {
  border-radius: 0;
}
.input-group-addon,
.input-group-btn {
  width: 1%;
  white-space: nowrap;
  vertical-align: middle;
}
.input-group-addon {
  padding: 6px 12px;
  font-size: 12px;
  font-weight: normal;
  line-height: 1;
  color: #555555;
  text-align: center;
  background-color: #f3f3f3;
  border: 1px solid #ebebeb;
  border-radius: 3px;
  color: #919191;
}
.input-group-addon.input-sm {
  padding: 5px 10px;
  font-size: 11px;
  border-radius: 2px;
}
.input-group-addon.input-lg {
  padding: 10px 16px;
  font-size: 15px;
  border-radius: 3px;
}
.input-group-addon input[type="radio"],
.input-group-addon input[type="checkbox"] {
  margin-top: 0;
}
.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child > .btn,
.input-group-btn:first-child > .dropdown-toggle,
.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle) {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
}
.input-group-addon:first-child {
  border-right: 0;
}
.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:last-child > .btn,
.input-group-btn:last-child > .dropdown-toggle,
.input-group-btn:first-child > .btn:not(:first-child) {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
}
.input-group-addon:last-child {
  border-left: 0;
}
.input-group-btn {
  position: relative;
  white-space: nowrap;
  font-size: 0;
}
.input-group-btn:first-child > .btn {
  margin-right: -1px;
}
.input-group-btn:last-child > .btn {
  margin-left: -1px;
}
.input-group-btn > .btn {
  position: relative;
}
.input-group-btn > .btn + .btn {
  margin-left: -1px;
}
.input-group-btn > .btn:hover,
.input-group-btn > .btn:active {
  z-index: 2;
}
.input-group > .twitter-typeahead:first-child .typeahead {
  -webkit-border-radius: 3px 0 0 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 0 0 3px;
  -moz-background-clip: padding;
  border-radius: 3px 0 0 3px;
  background-clip: padding-box;
}
.input-group > .twitter-typeahead:last-child .typeahead {
  -webkit-border-radius: 0 3px 3px 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 3px 3px 0;
  -moz-background-clip: padding;
  border-radius: 0 3px 3px 0;
  background-clip: padding-box;
}
.nav {
  margin-bottom: 0;
  padding-left: 0;
  list-style: none;
}
.nav:before,
.nav:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.nav:after {
  clear: both;
}
.nav:before,
.nav:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.nav:after {
  clear: both;
}
.nav > li {
  position: relative;
  display: block;
}
.nav > li > a {
  position: relative;
  display: block;
  padding: 10px 15px;
}
.nav > li > a:hover,
.nav > li > a:focus {
  text-decoration: none;
  background-color: #eeeeee;
}
.nav > li.disabled > a {
  color: #999999;
}
.nav > li.disabled > a:hover,
.nav > li.disabled > a:focus {
  color: #999999;
  text-decoration: none;
  background-color: transparent;
  cursor: not-allowed;
}
.nav .open > a,
.nav .open > a:hover,
.nav .open > a:focus {
  background-color: #eeeeee;
  border-color: #373e4a;
}
.nav .open > a .caret,
.nav .open > a:hover .caret,
.nav .open > a:focus .caret {
  border-top-color: #818da2;
  border-bottom-color: #818da2;
}
.nav .nav-divider {
  height: 1px;
  margin: 7.5px 0;
  overflow: hidden;
  background-color: #e5e5e5;
}
.nav > li > a > img {
  max-width: none;
}
.nav-tabs {
  border-bottom: 1px solid #ebebeb;
  margin-top: 20px;
  margin-bottom: 10px;
}
.nav-tabs > li {
  display: inline-block;
  margin-bottom: -1px;
}
.nav-tabs > li > a {
  margin-right: 4px;
  line-height: 1.42857143;
  border: 1px solid transparent;
  border-radius: 3px 3px 0 0;
}
.nav-tabs > li > a:hover {
  border-color: #eeeeee #eeeeee #ebebeb;
}
.nav-tabs > li:first-child > a {
  margin-left: 10px;
}
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  color: #555555;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-bottom-color: transparent;
  cursor: default;
}
.nav-tabs.nav-justified {
  width: 100%;
  border-bottom: 0;
}
.nav-tabs.nav-justified > li {
  float: none;
}
.nav-tabs.nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
}
.nav-tabs.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 768px) {
  .nav-tabs.nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-tabs.nav-justified > li > a {
    margin-bottom: 0;
  }
}
.nav-tabs.nav-justified > li > a {
  margin-right: 0;
  border-radius: 3px;
}
.nav-tabs.nav-justified > .active > a,
.nav-tabs.nav-justified > .active > a:hover,
.nav-tabs.nav-justified > .active > a:focus {
  border: 1px solid #dddddd;
}
@media (min-width: 768px) {
  .nav-tabs.nav-justified > li > a {
    border-bottom: 1px solid #dddddd;
    border-radius: 3px 3px 0 0;
  }
  .nav-tabs.nav-justified > .active > a,
  .nav-tabs.nav-justified > .active > a:hover,
  .nav-tabs.nav-justified > .active > a:focus {
    border-bottom-color: #ffffff;
  }
}
.nav-tabs.right-aligned {
  text-align: right;
}
.nav-tabs.right-aligned > li > a {
  margin-left: 4px;
  margin-right: 0;
}
.nav-tabs.right-aligned > li:last-child > a {
  margin-right: 10px;
}
.nav-tabs.bordered {
  margin-bottom: 0;
}
.nav-tabs.bordered > li:first-child > a {
  margin-left: 0;
}
.nav-tabs.bordered > li:last-child > a {
  margin-right: 0;
}
.nav-tabs.bordered + .tab-content {
  border: 1px solid #ebebeb;
  border-top: 0;
  -webkit-border-radius: 0 0 3px 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 0 3px 3px;
  -moz-background-clip: padding;
  border-radius: 0 0 3px 3px;
  background-clip: padding-box;
  padding: 10px 15px;
  margin-bottom: 20px;
}
.nav-tabs.bordered + .tab-content > p:last-child {
  margin-bottom: 0;
}
.nav-pills > li {
  float: left;
}
.nav-pills > li > a {
  border-radius: 3px;
}
.nav-pills > li + li {
  margin-left: 2px;
}
.nav-pills > li.active > a,
.nav-pills > li.active > a:hover,
.nav-pills > li.active > a:focus {
  color: #ffffff;
  background-color: #949494;
}
.nav-pills > li.active > a .caret,
.nav-pills > li.active > a:hover .caret,
.nav-pills > li.active > a:focus .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
}
.nav-stacked > li {
  float: none;
}
.nav-stacked > li + li {
  margin-top: 2px;
  margin-left: 0;
}
.nav-justified {
  width: 100%;
}
.nav-justified > li {
  float: none;
}
.nav-justified > li > a {
  text-align: center;
  margin-bottom: 5px;
}
.nav-justified > .dropdown .dropdown-menu {
  top: auto;
  left: auto;
}
@media (min-width: 768px) {
  .nav-justified > li {
    display: table-cell;
    width: 1%;
  }
  .nav-justified > li > a {
    margin-bottom: 0;
  }
}
.nav-tabs-justified {
  border-bottom: 0;
}
.nav-tabs-justified > li > a {
  margin-right: 0;
  border-radius: 3px;
}
.nav-tabs-justified > .active > a,
.nav-tabs-justified > .active > a:hover,
.nav-tabs-justified > .active > a:focus {
  border: 1px solid #dddddd;
}
@media (min-width: 768px) {
  .nav-tabs-justified > li > a {
    border-bottom: 1px solid #dddddd;
    border-radius: 3px 3px 0 0;
  }
  .nav-tabs-justified > .active > a,
  .nav-tabs-justified > .active > a:hover,
  .nav-tabs-justified > .active > a:focus {
    border-bottom-color: #ffffff;
  }
}
.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}
.nav .caret {
  border-top-color: #373e4a;
  border-bottom-color: #373e4a;
}
.nav a:hover .caret {
  border-top-color: #818da2;
  border-bottom-color: #818da2;
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.navbar {
  position: relative;
  min-height: 50px;
  margin-bottom: 17px;
  border: 1px solid transparent;
}
.navbar:before,
.navbar:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.navbar:after {
  clear: both;
}
.navbar:before,
.navbar:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.navbar:after {
  clear: both;
}
@media (min-width: 768px) {
  .navbar {
    border-radius: 3px;
  }
}
.navbar-header:before,
.navbar-header:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.navbar-header:after {
  clear: both;
}
.navbar-header:before,
.navbar-header:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.navbar-header:after {
  clear: both;
}
@media (min-width: 768px) {
  .navbar-header {
    float: left;
  }
}
.navbar-collapse {
  max-height: 340px;
  overflow-x: visible;
  padding-right: 15px;
  padding-left: 15px;
  border-top: 1px solid transparent;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-overflow-scrolling: touch;
}
.navbar-collapse:before,
.navbar-collapse:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.navbar-collapse:after {
  clear: both;
}
.navbar-collapse:before,
.navbar-collapse:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.navbar-collapse:after {
  clear: both;
}
.navbar-collapse.in {
  overflow-y: auto;
}
@media (min-width: 768px) {
  .navbar-collapse {
    width: auto;
    border-top: 0;
    box-shadow: none;
  }
  .navbar-collapse.collapse {
    display: block !important;
    height: auto !important;
    padding-bottom: 0;
    overflow: visible !important;
  }
  .navbar-collapse.in {
    overflow-y: auto;
  }
  .navbar-collapse .navbar-nav.navbar-left:first-child {
    margin-left: -15px;
  }
  .navbar-collapse .navbar-nav.navbar-right:last-child {
    margin-right: -15px;
  }
  .navbar-collapse .navbar-text:last-child {
    margin-right: 0;
  }
}
.container > .navbar-header,
.container > .navbar-collapse {
  margin-right: -15px;
  margin-left: -15px;
}
@media (min-width: 768px) {
  .container > .navbar-header,
  .container > .navbar-collapse {
    margin-right: 0;
    margin-left: 0;
  }
}
.navbar-static-top {
  z-index: 1000;
  border-width: 0 0 1px;
}
@media (min-width: 768px) {
  .navbar-static-top {
    border-radius: 0;
  }
}
.navbar-fixed-top,
.navbar-fixed-bottom {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 1030;
}
@media (min-width: 768px) {
  .navbar-fixed-top,
  .navbar-fixed-bottom {
    border-radius: 0;
  }
}
.navbar-fixed-top {
  top: 0;
  border-width: 0 0 1px;
}
.navbar-fixed-bottom {
  bottom: 0;
  margin-bottom: 0;
  border-width: 1px 0 0;
}
.navbar-brand {
  float: left;
  padding: 16.5px 15px;
  font-size: 15px;
  line-height: 17px;
}
.navbar-brand:hover,
.navbar-brand:focus {
  text-decoration: none;
}
@media (min-width: 768px) {
  .navbar > .container .navbar-brand {
    margin-left: -15px;
  }
}
.navbar-toggle {
  position: relative;
  float: right;
  margin-right: 15px;
  padding: 9px 10px;
  margin-top: 8px;
  margin-bottom: 8px;
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: 3px;
}
.navbar-toggle .icon-bar {
  display: block;
  width: 22px;
  height: 2px;
  border-radius: 1px;
}
.navbar-toggle .icon-bar + .icon-bar {
  margin-top: 4px;
}
@media (min-width: 768px) {
  .navbar-toggle {
    display: none;
  }
}
.navbar-nav {
  margin: 8.25px -15px;
}
.navbar-nav > li > a {
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 17px;
}
@media (max-width: 767px) {
  .navbar-nav .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    box-shadow: none;
  }
  .navbar-nav .open .dropdown-menu > li > a,
  .navbar-nav .open .dropdown-menu .dropdown-header {
    padding: 5px 15px 5px 25px;
  }
  .navbar-nav .open .dropdown-menu > li > a {
    line-height: 17px;
  }
  .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-nav .open .dropdown-menu > li > a:focus {
    background-image: none;
  }
}
@media (min-width: 768px) {
  .navbar-nav {
    float: left;
    margin: 0;
  }
  .navbar-nav > li {
    float: left;
  }
  .navbar-nav > li > a {
    padding-top: 16.5px;
    padding-bottom: 16.5px;
  }
}
@media (min-width: 768px) {
  .navbar-left {
    float: left !important;
  }
  .navbar-right {
    float: right !important;
  }
}
.navbar-form {
  margin-left: -15px;
  margin-right: -15px;
  padding: 10px 15px;
  border-top: 1px solid transparent;
  border-bottom: 1px solid transparent;
  -moz-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.1), 0 1px 0 rgba(255, 255, 255, 0.1);
  margin-top: 9.5px;
  margin-bottom: 9.5px;
}
@media (min-width: 768px) {
  .navbar-form .form-group {
    display: inline-block;
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .form-control {
    display: inline-block;
    width: auto;
    vertical-align: middle;
  }
  .navbar-form .input-group > .form-control {
    width: 100%;
  }
  .navbar-form .control-label {
    margin-bottom: 0;
    vertical-align: middle;
  }
  .navbar-form .radio,
  .navbar-form .checkbox {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 0;
    padding-left: 0;
    vertical-align: middle;
  }
  .navbar-form .radio input[type="radio"],
  .navbar-form .checkbox input[type="checkbox"] {
    float: none;
    margin-left: 0;
  }
  .navbar-form .has-feedback .form-control-feedback {
    top: 0;
  }
}
@media (max-width: 767px) {
  .navbar-form .form-group {
    margin-bottom: 5px;
  }
}
@media (min-width: 768px) {
  .navbar-form {
    width: auto;
    border: 0;
    margin-left: 0;
    margin-right: 0;
    padding-top: 0;
    padding-bottom: 0;
    -moz-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
  }
}
.navbar-nav > li > .dropdown-menu {
  margin-top: 0;
  border-top-right-radius: 0;
  border-top-left-radius: 0;
}
.navbar-fixed-bottom .navbar-nav > li > .dropdown-menu {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.navbar-nav.pull-right > li > .dropdown-menu,
.navbar-nav > li > .dropdown-menu.pull-right {
  left: auto;
  right: 0;
}
.navbar-btn {
  margin-top: 9.5px;
  margin-bottom: 9.5px;
}
.navbar-text {
  float: left;
  margin-top: 16.5px;
  margin-bottom: 16.5px;
}
@media (min-width: 768px) {
  .navbar-text {
    margin-left: 15px;
    margin-right: 15px;
  }
}
.navbar-default {
  background-color: #ffffff;
  border-color: #ebebeb;
}
.navbar-default .navbar-brand {
  color: #373e4a;
}
.navbar-default .navbar-brand:hover,
.navbar-default .navbar-brand:focus {
  color: #6e7b93;
  background-color: transparent;
}
.navbar-default .navbar-text {
  color: #373e4a;
}
.navbar-default .navbar-nav > li > a {
  color: #777777;
}
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus {
  color: #333333;
  background-color: transparent;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus {
  color: #555555;
  background-color: #f0f0f1;
}
.navbar-default .navbar-nav > .disabled > a,
.navbar-default .navbar-nav > .disabled > a:hover,
.navbar-default .navbar-nav > .disabled > a:focus {
  color: #cccccc;
  background-color: transparent;
}
.navbar-default .navbar-toggle {
  border-color: #dddddd;
}
.navbar-default .navbar-toggle:hover,
.navbar-default .navbar-toggle:focus {
  background-color: #dddddd;
}
.navbar-default .navbar-toggle .icon-bar {
  background-color: #888888;
}
.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
  border-color: #ebebeb;
}
.navbar-default .navbar-nav > .dropdown > a:hover .caret,
.navbar-default .navbar-nav > .dropdown > a:focus .caret {
  border-top-color: #333333;
  border-bottom-color: #333333;
}
.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:hover,
.navbar-default .navbar-nav > .open > a:focus {
  background-color: #f0f0f1;
  color: #555555;
}
.navbar-default .navbar-nav > .open > a .caret,
.navbar-default .navbar-nav > .open > a:hover .caret,
.navbar-default .navbar-nav > .open > a:focus .caret {
  border-top-color: #555555;
  border-bottom-color: #555555;
}
.navbar-default .navbar-nav > .dropdown > a .caret {
  border-top-color: #777777;
  border-bottom-color: #777777;
}
@media (max-width: 767px) {
  .navbar-default .navbar-nav .open .dropdown-menu > li > a {
    color: #777777;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #333333;
    background-color: transparent;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #555555;
    background-color: #f0f0f1;
  }
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .navbar-default .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #cccccc;
    background-color: transparent;
  }
}
.navbar-default .navbar-link {
  color: #777777;
}
.navbar-default .navbar-link:hover {
  color: #333333;
}
.navbar-inverse {
  background-color: #303641;
  border-color: #454a54;
}
.navbar-inverse .navbar-brand {
  color: #ffffff;
}
.navbar-inverse .navbar-brand:hover,
.navbar-inverse .navbar-brand:focus {
  color: rgba(255, 255, 255, 0.8);
  background-color: transparent;
}
.navbar-inverse .navbar-text {
  color: #aaabae;
}
.navbar-inverse .navbar-nav > li > a {
  color: #aaabae;
}
.navbar-inverse .navbar-nav > li > a:hover,
.navbar-inverse .navbar-nav > li > a:focus {
  color: #ffffff;
  background-color: transparent;
}
.navbar-inverse .navbar-nav > .active > a,
.navbar-inverse .navbar-nav > .active > a:hover,
.navbar-inverse .navbar-nav > .active > a:focus {
  color: #ffffff;
  background-color: #2b303a;
}
.navbar-inverse .navbar-nav > .disabled > a,
.navbar-inverse .navbar-nav > .disabled > a:hover,
.navbar-inverse .navbar-nav > .disabled > a:focus {
  color: #444444;
  background-color: transparent;
}
.navbar-inverse .navbar-toggle {
  border-color: #333333;
}
.navbar-inverse .navbar-toggle:hover,
.navbar-inverse .navbar-toggle:focus {
  background-color: #333333;
}
.navbar-inverse .navbar-toggle .icon-bar {
  background-color: #ffffff;
}
.navbar-inverse .navbar-collapse,
.navbar-inverse .navbar-form {
  border-color: #21252c;
}
.navbar-inverse .navbar-nav > .open > a,
.navbar-inverse .navbar-nav > .open > a:hover,
.navbar-inverse .navbar-nav > .open > a:focus {
  background-color: #2b303a;
  color: #ffffff;
}
.navbar-inverse .navbar-nav > .dropdown > a:hover .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
}
.navbar-inverse .navbar-nav > .dropdown > a .caret {
  border-top-color: #aaabae;
  border-bottom-color: #aaabae;
}
.navbar-inverse .navbar-nav > .open > a .caret,
.navbar-inverse .navbar-nav > .open > a:hover .caret,
.navbar-inverse .navbar-nav > .open > a:focus .caret {
  border-top-color: #ffffff;
  border-bottom-color: #ffffff;
}
@media (max-width: 767px) {
  .navbar-inverse .navbar-nav .open .dropdown-menu > .dropdown-header {
    border-color: #454a54;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a {
    color: #aaabae;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > li > a:focus {
    color: #ffffff;
    background-color: transparent;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .active > a:focus {
    color: #ffffff;
    background-color: #2b303a;
  }
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:hover,
  .navbar-inverse .navbar-nav .open .dropdown-menu > .disabled > a:focus {
    color: #444444;
    background-color: transparent;
  }
}
.navbar-inverse .navbar-link {
  color: #aaabae;
}
.navbar-inverse .navbar-link:hover {
  color: #ffffff;
}
.breadcrumb {
  padding: 8px 15px;
  margin-bottom: 17px;
  list-style: none;
  background-color: #f5f5f5;
  border-radius: 3px;
}
.breadcrumb > li {
  display: inline-block;
}
.breadcrumb > li i {
  margin-right: 5px;
  color: #737881;
}
.breadcrumb > li a {
  color: #737881;
  font-weight: 400;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.breadcrumb > li a:hover {
  color: #464e5e;
}
.breadcrumb > li + li:before {
  content: "/\00a0";
  padding: 0 5px;
  color: #cccccc;
}
.breadcrumb > .active {
  color: #999999;
  font-weight: 400;
}
.breadcrumb > .active > a {
  font-weight: 400;
}
.breadcrumb.bc-2,
.breadcrumb.breadcrumb-2 {
  background-color: transparent;
  border: 1px solid #ebebeb;
}
.breadcrumb.bc-3,
.breadcrumb.breadcrumb-3 {
  padding-left: 0;
  padding-right: 0;
  background-color: transparent;
}
.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 17px 0;
  border-radius: 3px;
}
.pagination > li {
  display: inline;
}
.pagination > li > a,
.pagination > li > span {
  position: relative;
  float: left;
  padding: 6px 12px;
  line-height: 1.42857143;
  text-decoration: none;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  margin-left: -1px;
}
.pagination > li:first-child > a,
.pagination > li:first-child > span {
  margin-left: 0;
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}
.pagination > li:last-child > a,
.pagination > li:last-child > span {
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
  background-color: #f5f5f6;
}
.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
  z-index: 2;
  color: #ffffff;
  background-color: #303641;
  border-color: #303641;
  cursor: default;
}
.pagination > .disabled > span,
.pagination > .disabled > span:hover,
.pagination > .disabled > span:focus,
.pagination > .disabled > a,
.pagination > .disabled > a:hover,
.pagination > .disabled > a:focus {
  color: #999999;
  background-color: #ffffff;
  border-color: #dddddd;
  cursor: not-allowed;
}
.pagination-lg > li > a,
.pagination-lg > li > span {
  padding: 10px 16px;
  font-size: 15px;
}
.pagination-lg > li:first-child > a,
.pagination-lg > li:first-child > span {
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}
.pagination-lg > li:last-child > a,
.pagination-lg > li:last-child > span {
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}
.pagination-sm > li > a,
.pagination-sm > li > span {
  padding: 5px 10px;
  font-size: 11px;
}
.pagination-sm > li:first-child > a,
.pagination-sm > li:first-child > span {
  border-bottom-left-radius: 2px;
  border-top-left-radius: 2px;
}
.pagination-sm > li:last-child > a,
.pagination-sm > li:last-child > span {
  border-bottom-right-radius: 2px;
  border-top-right-radius: 2px;
}
.pager {
  padding-left: 0;
  margin: 17px 0;
  list-style: none;
  text-align: center;
}
.pager:before,
.pager:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.pager:after {
  clear: both;
}
.pager:before,
.pager:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.pager:after {
  clear: both;
}
.pager li {
  display: inline;
}
.pager li > a,
.pager li > span {
  display: inline-block;
  padding: 5px 14px;
  background-color: #ffffff;
  border: 1px solid #dddddd;
  border-radius: 3px;
}
.pager li > a:hover,
.pager li > a:focus {
  text-decoration: none;
  background-color: #f5f5f6;
}
.pager .next > a,
.pager .next > span {
  float: right;
}
.pager .previous > a,
.pager .previous > span {
  float: left;
}
.pager .disabled > a,
.pager .disabled > a:hover,
.pager .disabled > a:focus,
.pager .disabled > span {
  color: #999999;
  background-color: #ffffff;
  cursor: not-allowed;
}
.label {
  display: inline;
  padding: 5px 10px;
  font-size: 75%;
  font-weight: 400;
  line-height: 1;
  background-color: #ebebeb;
  color: #ffffff;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 3px;
}
.label.medium {
  font-size: 12px;
}
.label.large {
  font-size: 15px;
}
.label[href]:hover,
.label[href]:focus {
  color: #6e7b93;
  text-decoration: none;
  cursor: pointer;
}
.label:empty {
  display: none;
}
div.label {
  padding-left: 20px;
  padding-right: 20px;
}
.label-default {
  background-color: #ebebeb;
  color: #303641;
}
.label-default[href]:hover,
.label-default[href]:focus {
  background-color: #d2d2d2;
}
.label-primary {
  background-color: #303641;
  color: #ffffff;
}
.label-primary[href]:hover,
.label-primary[href]:focus {
  background-color: #1a1e24;
}
.label-secondary {
  background-color: #ec5956;
}
.label-secondary[href]:hover,
.label-secondary[href]:focus {
  background-color: #e72c28;
}
.label-success {
  background-color: #00a651;
}
.label-success[href]:hover,
.label-success[href]:focus {
  background-color: #007338;
}
.label-info {
  background-color: #21a9e1;
}
.label-info[href]:hover,
.label-info[href]:focus {
  background-color: #1988b6;
}
.label-warning {
  background-color: #fad839;
}
.label-warning[href]:hover,
.label-warning[href]:focus {
  background-color: #f9ce07;
}
.label-danger {
  background-color: #cc2424;
}
.label-danger[href]:hover,
.label-danger[href]:focus {
  background-color: #a11c1c;
}
.badge {
  display: inline-block;
  min-width: 10px;
  padding: 3px 7px;
  font-size: 11px;
  font-weight: 400;
  color: #ffffff;
  line-height: 1;
  vertical-align: baseline;
  white-space: nowrap;
  text-align: center;
  background-color: #999999;
  border-radius: 10px;
  background-color: #ebebeb;
  color: #373e4a;
}
.badge.badge-primary {
  background-color: #303641;
  color: #ffffff;
}
.badge.badge-secondary {
  background-color: #ee4749;
  color: #ffffff;
}
.badge.badge-success {
  background-color: #00a651;
  color: #ffffff;
}
.badge.badge-info {
  background-color: #21a9e1;
  color: #ffffff;
}
.badge.badge-warning {
  background-color: #fad839;
  color: #ffffff;
}
.badge.badge-danger {
  background-color: #cc2424;
  color: #ffffff;
}
.badge.badge-roundless {
  -webkit-border-radius: 1px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 1px;
  -moz-background-clip: padding;
  border-radius: 1px;
  background-clip: padding-box;
}
.badge:empty {
  display: none;
}
a.badge:hover,
a.badge:focus {
  color: #ffffff;
  text-decoration: none;
  cursor: pointer;
}
.btn .badge {
  position: relative;
  top: -1px;
}
a.list-group-item.active > .badge,
.nav-pills > .active > a > .badge {
  color: #373e4a;
  background-color: #ffffff;
}
.nav-pills > li > a > .badge {
  margin-left: 3px;
}
.jumbotron {
  padding: 40px;
  margin-bottom: 40px;
  color: #303641;
  background-color: #eeeeee;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.jumbotron h1 {
  line-height: 1;
  color: inherit;
  margin-top: 10px;
  margin-bottom: 20px;
}
.jumbotron p {
  line-height: 1.4;
  font-weight: 200;
}
.container .jumbotron {
  border-radius: 3px;
}
@media screen and (min-width: 768px) {
  .jumbotron {
    padding-top: 32px;
    padding-bottom: 32px;
  }
  .container .jumbotron {
    padding-left: 80px;
    padding-right: 80px;
  }
  .jumbotron h1 {
    font-size: 54px;
  }
}
.thumbnail {
  padding: 2px;
  line-height: 1.42857143;
  background-color: #ffffff;
  border: 1px solid #ededf0;
  border-radius: 3px;
  -moz-transition: all 0.2s ease-in-out;
  -o-transition: all 0.2s ease-in-out;
  -webkit-transition: all 0.2s ease-in-out;
  transition: all 0.2s ease-in-out;
  display: inline-block;
  max-width: 100%;
  height: auto;
  display: block;
  margin-bottom: 17px;
}
.thumbnail > img {
  display: block;
  max-width: 100%;
  height: auto;
  margin-left: auto;
  margin-right: auto;
}
a.thumbnail:hover,
a.thumbnail:focus,
a.thumbnail.active {
  border-color: #373e4a;
}
.thumbnail .caption {
  padding: 9px;
  color: #949494;
}
.alert {
  padding: 15px;
  margin-bottom: 17px;
  border: 1px solid transparent;
  border-radius: 3px;
}
.alert h4 {
  margin-top: 0;
  color: inherit;
}
.alert .alert-link {
  font-weight: bold;
}
.alert > p,
.alert > ul {
  margin-bottom: 0;
}
.alert > p + p {
  margin-top: 5px;
}
.alert-dismissable {
  padding-right: 35px;
}
.alert-dismissable .close {
  position: relative;
  top: -2px;
  right: -21px;
  color: inherit;
}
.alert-success {
  background-color: #bdedbc;
  border-color: #bdedbc;
  color: #045702;
}
.alert-success hr {
  border-top-color: #a2e294;
}
.alert-success .alert-link {
  color: #022501;
}
.alert-info {
  background-color: #c5e8f7;
  border-color: #c5e8f7;
  color: #2c7ea1;
}
.alert-info hr {
  border-top-color: #8fe3f0;
}
.alert-info .alert-link {
  color: #215f79;
}
.alert-warning {
  background-color: #ffefa4;
  border-color: #ffefa4;
  color: #574802;
}
.alert-warning hr {
  border-top-color: #ffce71;
}
.alert-warning .alert-link {
  color: #251f01;
}
.alert-danger {
  background-color: #ffc9c9;
  border-color: #ffc9c9;
  color: #ac1818;
}
.alert-danger hr {
  border-top-color: #ff96a7;
}
.alert-danger .alert-link {
  color: #7f1212;
}
.alert-default {
  background-color: #ebebeb;
  border-color: #ebebeb;
  color: #303641;
}
.alert-default hr {
  border-top-color: #dedede;
}
.alert-default .alert-link {
  color: #1a1e24;
}
.alert-minimal {
  background-color: #ebebeb;
  border-color: #ebebeb;
  color: #303641;
  background-color: #ffffff;
}
.alert-minimal hr {
  border-top-color: #dedede;
}
.alert-minimal .alert-link {
  color: #1a1e24;
}
@-webkit-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
@-moz-keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
@-o-keyframes progress-bar-stripes {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 40px 0;
  }
}
@keyframes progress-bar-stripes {
  from {
    background-position: 40px 0;
  }
  to {
    background-position: 0 0;
  }
}
.progress {
  overflow: hidden;
  height: 17px;
  margin-bottom: 17px;
  background-color: #f5f5f5;
  -webkit-border-radius: 10px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 10px;
  -moz-background-clip: padding;
  border-radius: 10px;
  background-clip: padding-box;
}
.progress-bar {
  float: left;
  width: 0%;
  height: 100%;
  font-size: 11px;
  line-height: 17px;
  color: #ffffff;
  text-align: center;
  background-color: #949494;
  -moz-transition: width 0.6s ease;
  -o-transition: width 0.6s ease;
  -webkit-transition: width 0.6s ease;
  transition: width 0.6s ease;
}
.progress-striped .progress-bar {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 40px 40px;
}
.progress.active .progress-bar {
  -moz-animation: progress-bar-stripes 2s linear infinite;
  -o-animation: progress-bar-stripes 2s linear infinite;
  -webkit-animation: progress-bar-stripes 2s linear infinite;
  animation: progress-bar-stripes 2s linear infinite;
}
.progress-bar-success {
  background-color: #00a651;
}
.progress-striped .progress-bar-success {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-info {
  background-color: #21a9e1;
}
.progress-striped .progress-bar-info {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-warning {
  background-color: #fad839;
}
.progress-striped .progress-bar-warning {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-danger {
  background-color: #cc2424;
}
.progress-striped .progress-bar-danger {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-default {
  background: #fff;
  padding: 2px;
  border: 1px solid #ebebeb;
}
.progress-bar-default .progress-bar {
  background-color: #ebebeb;
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, #e1e1e1), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, #e1e1e1), color-stop(0.75, #e1e1e1), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, #e1e1e1 25%, transparent 25%, transparent 50%, #e1e1e1 50%, #e1e1e1 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, #e1e1e1 25%, transparent 25%, transparent 50%, #e1e1e1 50%, #e1e1e1 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, #e1e1e1 25%, transparent 25%, transparent 50%, #e1e1e1 50%, #e1e1e1 75%, transparent 75%, transparent);
  -webkit-border-radius: 10px 0 0 10px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 10px 0 0 10px;
  -moz-background-clip: padding;
  border-radius: 10px 0 0 10px;
  background-clip: padding-box;
}
.progress-striped .progress-bar-default .progress-bar {
  background-image: -webkit-gradient(linear, 0 100%, 100% 0, color-stop(0.25, rgba(255, 255, 255, 0.15)), color-stop(0.25, transparent), color-stop(0.5, transparent), color-stop(0.5, rgba(255, 255, 255, 0.15)), color-stop(0.75, rgba(255, 255, 255, 0.15)), color-stop(0.75, transparent), to(transparent));
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: -moz-linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
}
.progress-bar-default .progress-bar[aria-valuenow="100"] {
  -webkit-border-radius: 10px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 10px;
  -moz-background-clip: padding;
  border-radius: 10px;
  background-clip: padding-box;
}
.media,
.media-body {
  overflow: hidden;
  zoom: 1;
}
.media,
.media .media {
  margin-top: 15px;
}
.media:first-child {
  margin-top: 0;
}
.media-object {
  display: block;
}
.media-heading {
  margin: 0 0 5px;
}
.media > .pull-left {
  margin-right: 10px;
}
.media > .pull-right {
  margin-left: 10px;
}
.media-list {
  padding-left: 0;
  list-style: none;
}
.list-group {
  margin-bottom: 20px;
  padding-left: 0;
}
.list-group-item {
  position: relative;
  display: block;
  padding: 10px 15px;
  margin-bottom: -1px;
  background-color: #ffffff;
  border: 1px solid #dddddd;
}
.list-group-item:first-child {
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.list-group-item:last-child {
  margin-bottom: 0;
  border-bottom-right-radius: 3px;
  border-bottom-left-radius: 3px;
}
.list-group-item > .badge {
  float: right;
}
.list-group-item > .badge + .badge {
  margin-right: 5px;
}
.list-group-item-success {
  color: #045702;
  background-color: #bdedbc;
}
a.list-group-item-success {
  color: #045702;
}
a.list-group-item-success .list-group-item-heading {
  color: inherit;
}
a.list-group-item-success:hover,
a.list-group-item-success:focus {
  color: #045702;
  background-color: #a9e8a8;
}
a.list-group-item-success.active,
a.list-group-item-success.active:hover,
a.list-group-item-success.active:focus {
  color: #fff;
  background-color: #045702;
  border-color: #045702;
}
.list-group-item-info {
  color: #2c7ea1;
  background-color: #c5e8f7;
}
a.list-group-item-info {
  color: #2c7ea1;
}
a.list-group-item-info .list-group-item-heading {
  color: inherit;
}
a.list-group-item-info:hover,
a.list-group-item-info:focus {
  color: #2c7ea1;
  background-color: #afdff4;
}
a.list-group-item-info.active,
a.list-group-item-info.active:hover,
a.list-group-item-info.active:focus {
  color: #fff;
  background-color: #2c7ea1;
  border-color: #2c7ea1;
}
.list-group-item-warning {
  color: #574802;
  background-color: #ffefa4;
}
a.list-group-item-warning {
  color: #574802;
}
a.list-group-item-warning .list-group-item-heading {
  color: inherit;
}
a.list-group-item-warning:hover,
a.list-group-item-warning:focus {
  color: #574802;
  background-color: #ffeb8a;
}
a.list-group-item-warning.active,
a.list-group-item-warning.active:hover,
a.list-group-item-warning.active:focus {
  color: #fff;
  background-color: #574802;
  border-color: #574802;
}
.list-group-item-danger {
  color: #ac1818;
  background-color: #ffc9c9;
}
a.list-group-item-danger {
  color: #ac1818;
}
a.list-group-item-danger .list-group-item-heading {
  color: inherit;
}
a.list-group-item-danger:hover,
a.list-group-item-danger:focus {
  color: #ac1818;
  background-color: #ffafaf;
}
a.list-group-item-danger.active,
a.list-group-item-danger.active:hover,
a.list-group-item-danger.active:focus {
  color: #fff;
  background-color: #ac1818;
  border-color: #ac1818;
}
a.list-group-item {
  color: #555555;
}
a.list-group-item .list-group-item-heading {
  color: #333333;
}
a.list-group-item:hover,
a.list-group-item:focus {
  text-decoration: none;
  background-color: #f5f5f6;
}
a.list-group-item.active,
a.list-group-item.active:hover,
a.list-group-item.active:focus {
  z-index: 2;
  color: #ffffff;
  background-color: #303641;
  border-color: #303641;
}
a.list-group-item.active .list-group-item-heading,
a.list-group-item.active:hover .list-group-item-heading,
a.list-group-item.active:focus .list-group-item-heading {
  color: inherit;
}
a.list-group-item.active .list-group-item-text,
a.list-group-item.active:hover .list-group-item-text,
a.list-group-item.active:focus .list-group-item-text {
  color: #909aad;
}
.list-group-item-heading {
  margin-top: 0;
  margin-bottom: 5px;
}
.list-group-item-text {
  margin-bottom: 0;
  line-height: 1.3;
}
.panel {
  margin-bottom: 17px;
  background-color: #ffffff;
  border: 1px solid transparent;
  border-radius: 3px;
}
.panel.panel-shadow {
  -moz-box-shadow: 0 3px 1px rgba(0,0,0,.04);
  -webkit-box-shadow: 0 3px 1px rgba(0,0,0,.04);
  box-shadow: 0 3px 1px rgba(0,0,0,.04);
}
.panel.reloading > .panel-heading > .panel-options > a[rel="reload"] > i {
  -moz-transition: none;
  -o-transition: none;
  -webkit-transition: none;
  transition: none;
  -moz-animation: reloadingAnimation 1s linear infinite;
  -o-animation: reloadingAnimation 1s linear infinite;
  -webkit-animation: reloadingAnimation 1s linear infinite;
  animation: reloadingAnimation 1s linear infinite;
}
.panel.panel-collapse > .panel-heading > .panel-options > a[rel="collapse"] > i {
  -moz-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.panel .blockUI.blockOverlay {
  background-image: url(../image/loader-1.gif);
  background-image: url(../image/loader-2.gif);
  background-repeat: no-repeat;
  background-position: center center;
}
.panel[data-collapsed="1"] .panel-body {
  display: none;
}
.panel[data-collapsed="1"] .panel-heading > .panel-options > a[rel="collapse"] > i {
  -moz-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.panel.minimal > .panel-heading {
  padding: 0;
  border-bottom: 1px solid #ebebeb;
}
.panel.minimal > .panel-heading > .panel-title {
  position: relative;
  color: #373e4a;
  padding: 0;
  padding-bottom: 10px;
  top: 8px;
}
.panel.minimal > .panel-heading > .panel-options {
  padding-right: 0;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs > li a {
  padding: 8px 10px;
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs > li a i {
  color: #d0d3d9;
  font-size: 14px;
  line-height: 1;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs > li a:hover {
  background-color: #f0f0f1;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs > li.active a {
  border-bottom-color: #fff;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs > li.active a:hover {
  background-color: #fff;
}
.panel.minimal > .panel-heading > .panel-options > .nav-tabs > li.active a i {
  color: #303641;
}
.panel.minimal > .panel-body {
  padding-left: 0;
  padding-right: 0;
}
.panel.minimal.minimal-gray > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #f3f3f3;
  border-color: #ebebeb;
  color: rgba(115, 120, 129, 0.75);
}
.panel.minimal.minimal-gray > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
  color: #303641;
}
.panel-body {
  position: relative;
  padding: 15px;
}
.panel-body:before,
.panel-body:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.panel-body:after {
  clear: both;
}
.panel-body:before,
.panel-body:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.panel-body:after {
  clear: both;
}
.panel-body.no-padding {
  padding: 0;
}
.panel > .list-group {
  margin-bottom: 0;
}
.panel > .list-group .list-group-item {
  border-width: 1px 0;
  border-radius: 0;
}
.panel > .list-group .list-group-item:first-child {
  border-top: 0;
}
.panel > .list-group .list-group-item:last-child {
  border-bottom: 0;
}
.panel > .list-group:first-child .list-group-item:first-child {
  border-top-right-radius: 2px;
  border-top-left-radius: 2px;
}
.panel > .list-group:last-child .list-group-item:last-child {
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 2px;
}
.panel-heading + .list-group .list-group-item:first-child {
  border-top-width: 0;
}
.panel > .table,
.panel > .table-responsive > .table {
  margin-bottom: 0;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:first-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:first-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:first-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:first-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:first-child {
  border-top-left-radius: 2px;
}
.panel > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child td:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child td:last-child,
.panel > .table:first-child > thead:first-child > tr:first-child th:last-child,
.panel > .table-responsive:first-child > .table:first-child > thead:first-child > tr:first-child th:last-child,
.panel > .table:first-child > tbody:first-child > tr:first-child th:last-child,
.panel > .table-responsive:first-child > .table:first-child > tbody:first-child > tr:first-child th:last-child {
  border-top-right-radius: 2px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:first-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:first-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:first-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:first-child {
  border-bottom-left-radius: 2px;
}
.panel > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child td:last-child,
.panel > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table-responsive:last-child > .table:last-child > tbody:last-child > tr:last-child th:last-child,
.panel > .table:last-child > tfoot:last-child > tr:last-child th:last-child,
.panel > .table-responsive:last-child > .table:last-child > tfoot:last-child > tr:last-child th:last-child {
  border-bottom-right-radius: 2px;
}
.panel > .panel-body + .table,
.panel > .panel-body + .table-responsive {
  border-top: 1px solid #ebebeb;
}
.panel > .table > tbody:first-child > tr:first-child th,
.panel > .table > tbody:first-child > tr:first-child td {
  border-top: 0;
}
.panel > .table-bordered,
.panel > .table-responsive > .table-bordered {
  border: 0;
}
.panel > .table-bordered > thead > tr > th:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:first-child,
.panel > .table-bordered > tbody > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:first-child,
.panel > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:first-child,
.panel > .table-bordered > thead > tr > td:first-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:first-child,
.panel > .table-bordered > tbody > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:first-child,
.panel > .table-bordered > tfoot > tr > td:first-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:first-child {
  border-left: 0;
}
.panel > .table-bordered > thead > tr > th:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > th:last-child,
.panel > .table-bordered > tbody > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > th:last-child,
.panel > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > th:last-child,
.panel > .table-bordered > thead > tr > td:last-child,
.panel > .table-responsive > .table-bordered > thead > tr > td:last-child,
.panel > .table-bordered > tbody > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tbody > tr > td:last-child,
.panel > .table-bordered > tfoot > tr > td:last-child,
.panel > .table-responsive > .table-bordered > tfoot > tr > td:last-child {
  border-right: 0;
}
.panel > .table-bordered > thead > tr:first-child > th,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > th,
.panel > .table-bordered > tbody > tr:first-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > th,
.panel > .table-bordered > tfoot > tr:first-child > th,
.panel > .table-responsive > .table-bordered > tfoot > tr:first-child > th,
.panel > .table-bordered > thead > tr:first-child > td,
.panel > .table-responsive > .table-bordered > thead > tr:first-child > td,
.panel > .table-bordered > tbody > tr:first-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:first-child > td,
.panel > .table-bordered > tfoot > tr:first-child > td,
.panel > .table-responsive > .table-bordered > tfoot > tr:first-child > td {
  border-top: 0;
}
.panel > .table-bordered > thead > tr:last-child > th,
.panel > .table-responsive > .table-bordered > thead > tr:last-child > th,
.panel > .table-bordered > tbody > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > th,
.panel > .table-bordered > tfoot > tr:last-child > th,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > th,
.panel > .table-bordered > thead > tr:last-child > td,
.panel > .table-responsive > .table-bordered > thead > tr:last-child > td,
.panel > .table-bordered > tbody > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tbody > tr:last-child > td,
.panel > .table-bordered > tfoot > tr:last-child > td,
.panel > .table-responsive > .table-bordered > tfoot > tr:last-child > td {
  border-bottom: 0;
}
.panel > .table-responsive {
  border: 0;
  margin-bottom: 0;
}
.panel-heading {
  border-bottom: 1px solid transparent;
  border-top-right-radius: 3px;
  border-top-left-radius: 3px;
}
.panel-heading:before,
.panel-heading:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.panel-heading:after {
  clear: both;
}
.panel-heading:before,
.panel-heading:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.panel-heading:after {
  clear: both;
}
.panel-heading > .dropdown .dropdown-toggle {
  color: inherit;
}
.panel-heading > .panel-title {
  float: left;
  padding: 10px 15px;
}
.panel-heading > .panel-title > * {
  margin: 0;
}
.panel-heading > .panel-title > span {
  font-weight: normal;
}
.panel-heading > .panel-options {
  float: right;
  padding-right: 15px;
}
.panel-heading > .panel-options > a {
  margin-top: 10px;
}
.panel-heading > .panel-options > a[rel="reload"] > i {
  -o-transform-origin: 50%;
  -ms-transform-origin: 50%;
  -webkit-transform-origin: 50%;
  -moz-transform-origin: 50%;
  transform-origin: 50%;
}
.panel-heading > .panel-options > a[rel="collapse"] > i {
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-heading > .panel-options > .nav-tabs {
  margin-top: 0;
  margin-bottom: 0;
}
.panel-heading > .panel-options > .nav-tabs > li {
  margin-right: 4px;
}
.panel-heading > .panel-options > .nav-tabs > li:last-child {
  margin-right: 0;
}
lesshat-selector {
  -lh-property: 0; } 
@-webkit-keyframes reloadingAnimation{ 0%{ -webkit-transform: rotate(0deg); } 100%{ -webkit-transform: rotate(360deg); }}
@-moz-keyframes reloadingAnimation{ 0%{ -moz-transform: rotate(0deg); } 100%{ -moz-transform: rotate(360deg); }}
@-o-keyframes reloadingAnimation{ 0%{ -o-transform: rotate(0deg); } 100%{ -o-transform: rotate(360deg); }}
@keyframes reloadingAnimation{ 0%{-webkit-transform: rotate(0deg);-moz-transform: rotate(0deg);-ms-transform: rotate(0deg);transform: rotate(0deg); } 100%{-webkit-transform: rotate(360deg);-moz-transform: rotate(360deg);-ms-transform: rotate(360deg);transform: rotate(360deg); };
}
.panel-title {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 14px;
}
.panel-title > a {
  color: inherit;
}
.panel-footer {
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-top: 1px solid #ebebeb;
  border-bottom-right-radius: 2px;
  border-bottom-left-radius: 2px;
}
.panel-group {
  margin-bottom: 17px;
}
.panel-group .panel {
  margin-bottom: 0;
  overflow: hidden;
}
.panel-group .panel > .panel-heading > .panel-title {
  float: none;
}
.panel-group .panel > .panel-heading > .panel-title > a {
  display: block;
}
.panel-group .panel + .panel {
  margin-top: 5px;
}
.panel-group .panel-heading {
  border-bottom: 0;
}
.panel-group .panel-heading + .panel-collapse .panel-body {
  border-top: 1px solid #ebebeb;
}
.panel-group .panel-footer {
  border-top: 0;
}
.panel-group .panel-footer + .panel-collapse .panel-body {
  border-bottom: 1px solid #ebebeb;
}
.panel-group.joined > .panel {
  -webkit-border-radius: 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0;
  -moz-background-clip: padding;
  border-radius: 0;
  background-clip: padding-box;
}
.panel-group.joined > .panel + .panel {
  margin-top: 0;
  border-top: 0;
}
.panel-group.joined > .panel > .panel-heading {
  background-color: #ffffff;
}
.panel-group.joined > .panel > .panel-heading h4 a:before {
  position: relative;
  content: '\e87a';
  display: inline-block;
  font-family: 'Entypo';
  color: rgba(115, 120, 129, 0.7);
  padding: 10px 15px;
  padding: 0;
  float: right;
  font-size: 17px;
  margin-left: 13px;
  top: 0px;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-group.joined > .panel > .panel-heading h4 a.collapsed:before {
  -moz-transform: rotate(180deg);
  -o-transform: rotate(180deg);
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}
.panel-group.joined > .panel > .panel-heading + .panel-collapse {
  background-color: #f5f5f6;
  margin-bottom: 0;
  font-size: 12px;
}
.panel-group.joined > .panel:first-child {
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
}
.panel-group.joined > .panel:last-child {
  -webkit-border-radius: 0 0 3px 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 0 0 3px 3px;
  -moz-background-clip: padding;
  border-radius: 0 0 3px 3px;
  background-clip: padding-box;
}
.panel-primary {
  border-color: #ebebeb;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-primary > .panel-heading {
  color: #373e4a;
  background-color: #ffffff;
  border-color: #ebebeb;
}
.panel-primary > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ebebeb;
}
.panel-primary > .panel-heading > .dropdown .caret {
  border-color: #373e4a transparent;
}
.panel-primary > .panel-heading > .panel-title > a {
  color: #373e4a;
}
.panel-primary > .panel-heading > .panel-options > a {
  display: inline-block;
  color: rgba(55, 62, 74, 0.7);
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-primary > .panel-heading > .panel-options > a.bg {
  background-color: #f8f8f8;
  margin-left: 5px;
}
.panel-primary > .panel-heading > .panel-options > a.bg:hover {
  background-color: #ffffff;
}
.panel-primary > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #ebebeb;
  padding-top: 5px;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #ebebeb;
  background-color: #ebebeb;
  padding: 8px 10px;
  color: rgba(55, 62, 74, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #ffffff;
  background-color: #ffffff;
  color: #373e4a;
}
.panel-primary > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #373e4a;
}
.panel-primary > .panel-footer {
  background-color: #ffffff;
  color: #373e4a;
  border-top-color: #ebebeb;
}
.panel-primary > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ebebeb;
}
.panel-primary > .panel-body + .panel-body {
  border-top-color: #ebebeb;
}
.panel-invert {
  border-color: #39414e;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-invert > .panel-heading {
  color: #ffffff;
  background-color: #303641;
  border-color: #39414e;
}
.panel-invert > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #39414e;
}
.panel-invert > .panel-heading > .dropdown .caret {
  border-color: #ffffff transparent;
}
.panel-invert > .panel-heading > .panel-title > a {
  color: #ffffff;
}
.panel-invert > .panel-heading > .panel-options > a {
  display: inline-block;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-invert > .panel-heading > .panel-options > a.bg {
  background-color: #4f5a6b;
  margin-left: 5px;
}
.panel-invert > .panel-heading > .panel-options > a.bg:hover {
  background-color: #556174;
}
.panel-invert > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #39414e;
  padding-top: 5px;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #39414e;
  background-color: #39414e;
  padding: 8px 10px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #303641;
  background-color: #303641;
  color: #ffffff;
}
.panel-invert > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #ffffff;
}
.panel-invert > .panel-footer {
  background-color: #303641;
  color: #ffffff;
  border-top-color: #39414e;
}
.panel-invert > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #39414e;
}
.panel-invert > .panel-body + .panel-body {
  border-top-color: #39414e;
}
.panel-invert > .panel-body {
  background-color: #303641;
  color: #778193;
}
.panel-default {
  border-color: #ebebeb;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-default > .panel-heading {
  color: #373e4a;
  background-color: #f0f0f1;
  border-color: #ebebeb;
}
.panel-default > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ebebeb;
}
.panel-default > .panel-heading > .dropdown .caret {
  border-color: #373e4a transparent;
}
.panel-default > .panel-heading > .panel-title > a {
  color: #373e4a;
}
.panel-default > .panel-heading > .panel-options > a {
  display: inline-block;
  color: rgba(55, 62, 74, 0.7);
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-default > .panel-heading > .panel-options > a.bg {
  background-color: #dedede;
  margin-left: 5px;
}
.panel-default > .panel-heading > .panel-options > a.bg:hover {
  background-color: #e6e6e6;
}
.panel-default > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #ebebeb;
  padding-top: 5px;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #ebebeb;
  background-color: #ebebeb;
  padding: 8px 10px;
  color: rgba(55, 62, 74, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #f0f0f1;
  background-color: #f0f0f1;
  color: #373e4a;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #373e4a;
}
.panel-default > .panel-footer {
  background-color: #f0f0f1;
  color: #373e4a;
  border-top-color: #ebebeb;
}
.panel-default > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ebebeb;
}
.panel-default > .panel-body + .panel-body {
  border-top-color: #ebebeb;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #dedede;
}
.panel-default > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-success {
  border-color: #b4e8a8;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-success > .panel-heading {
  color: #045702;
  background-color: #bdedbc;
  border-color: #b4e8a8;
}
.panel-success > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #b4e8a8;
}
.panel-success > .panel-heading > .dropdown .caret {
  border-color: #045702 transparent;
}
.panel-success > .panel-heading > .panel-title > a {
  color: #045702;
}
.panel-success > .panel-heading > .panel-options > a {
  display: inline-block;
  color: rgba(4, 87, 2, 0.7);
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-success > .panel-heading > .panel-options > a.bg {
  background-color: #a2e294;
  margin-left: 5px;
}
.panel-success > .panel-heading > .panel-options > a.bg:hover {
  background-color: #ade5a0;
}
.panel-success > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #b4e8a8;
  padding-top: 5px;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #b4e8a8;
  background-color: #b4e8a8;
  padding: 8px 10px;
  color: rgba(4, 87, 2, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #bdedbc;
  background-color: #bdedbc;
  color: #045702;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #045702;
}
.panel-success > .panel-footer {
  background-color: #bdedbc;
  color: #045702;
  border-top-color: #b4e8a8;
}
.panel-success > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #b4e8a8;
}
.panel-success > .panel-body + .panel-body {
  border-top-color: #b4e8a8;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #91dd80;
}
.panel-success > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-warning {
  border-color: #ffd78a;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-warning > .panel-heading {
  color: #574802;
  background-color: #ffefa4;
  border-color: #ffd78a;
}
.panel-warning > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ffd78a;
}
.panel-warning > .panel-heading > .dropdown .caret {
  border-color: #574802 transparent;
}
.panel-warning > .panel-heading > .panel-title > a {
  color: #574802;
}
.panel-warning > .panel-heading > .panel-options > a {
  display: inline-block;
  color: rgba(87, 72, 2, 0.7);
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-warning > .panel-heading > .panel-options > a.bg {
  background-color: #ffce71;
  margin-left: 5px;
}
.panel-warning > .panel-heading > .panel-options > a.bg:hover {
  background-color: #ffd480;
}
.panel-warning > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #ffd78a;
  padding-top: 5px;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #ffd78a;
  background-color: #ffd78a;
  padding: 8px 10px;
  color: rgba(87, 72, 2, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #ffefa4;
  background-color: #ffefa4;
  color: #574802;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #574802;
}
.panel-warning > .panel-footer {
  background-color: #ffefa4;
  color: #574802;
  border-top-color: #ffd78a;
}
.panel-warning > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ffd78a;
}
.panel-warning > .panel-body + .panel-body {
  border-top-color: #ffd78a;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #ffe258;
}
.panel-warning > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-danger {
  border-color: #ffafbd;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-danger > .panel-heading {
  color: #ac1818;
  background-color: #ffc9c9;
  border-color: #ffafbd;
}
.panel-danger > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #ffafbd;
}
.panel-danger > .panel-heading > .dropdown .caret {
  border-color: #ac1818 transparent;
}
.panel-danger > .panel-heading > .panel-title > a {
  color: #ac1818;
}
.panel-danger > .panel-heading > .panel-options > a {
  display: inline-block;
  color: rgba(172, 24, 24, 0.7);
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-danger > .panel-heading > .panel-options > a.bg {
  background-color: #ff96a7;
  margin-left: 5px;
}
.panel-danger > .panel-heading > .panel-options > a.bg:hover {
  background-color: #ffa5b4;
}
.panel-danger > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #ffafbd;
  padding-top: 5px;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #ffafbd;
  background-color: #ffafbd;
  padding: 8px 10px;
  color: rgba(172, 24, 24, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #ffc9c9;
  background-color: #ffc9c9;
  color: #ac1818;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #ac1818;
}
.panel-danger > .panel-footer {
  background-color: #ffc9c9;
  color: #ac1818;
  border-top-color: #ffafbd;
}
.panel-danger > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #ffafbd;
}
.panel-danger > .panel-body + .panel-body {
  border-top-color: #ffafbd;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #ff7c7c;
}
.panel-danger > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-info {
  border-color: #a6e8f3;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-info > .panel-heading {
  color: #2c7ea1;
  background-color: #c5e8f7;
  border-color: #a6e8f3;
}
.panel-info > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #a6e8f3;
}
.panel-info > .panel-heading > .dropdown .caret {
  border-color: #2c7ea1 transparent;
}
.panel-info > .panel-heading > .panel-title > a {
  color: #2c7ea1;
}
.panel-info > .panel-heading > .panel-options > a {
  display: inline-block;
  color: rgba(44, 126, 161, 0.7);
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-info > .panel-heading > .panel-options > a.bg {
  background-color: #8fe3f0;
  margin-left: 5px;
}
.panel-info > .panel-heading > .panel-options > a.bg:hover {
  background-color: #9de6f1;
}
.panel-info > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #a6e8f3;
  padding-top: 5px;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #a6e8f3;
  background-color: #a6e8f3;
  padding: 8px 10px;
  color: rgba(44, 126, 161, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #c5e8f7;
  background-color: #c5e8f7;
  color: #2c7ea1;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #2c7ea1;
}
.panel-info > .panel-footer {
  background-color: #c5e8f7;
  color: #2c7ea1;
  border-top-color: #a6e8f3;
}
.panel-info > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #a6e8f3;
}
.panel-info > .panel-body + .panel-body {
  border-top-color: #a6e8f3;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #82cdee;
}
.panel-info > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
}
.panel-dark {
  border-color: #222222;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-dark > .panel-heading {
  color: #ffffff;
  background-color: #333333;
  border-color: #222222;
}
.panel-dark > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #222222;
}
.panel-dark > .panel-heading > .dropdown .caret {
  border-color: #ffffff transparent;
}
.panel-dark > .panel-heading > .panel-title > a {
  color: #ffffff;
}
.panel-dark > .panel-heading > .panel-options > a {
  display: inline-block;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-dark > .panel-heading > .panel-options > a.bg {
  background-color: #484848;
  margin-left: 5px;
}
.panel-dark > .panel-heading > .panel-options > a.bg:hover {
  background-color: #505050;
}
.panel-dark > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #222222;
  padding-top: 5px;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #222222;
  background-color: #222222;
  padding: 8px 10px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #333333;
  background-color: #333333;
  color: #ffffff;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #ffffff;
}
.panel-dark > .panel-footer {
  background-color: #333333;
  color: #ffffff;
  border-top-color: #222222;
}
.panel-dark > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #222222;
}
.panel-dark > .panel-body + .panel-body {
  border-top-color: #222222;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  border-color: rgba(34, 34, 34, 0.1);
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #fff;
  border-bottom-color: #fff;
  color: #222222;
}
.panel-dark > .panel-heading > .panel-options > .nav-tabs > li.active > a i {
  color: #222222;
}
.panel-gray {
  border-color: #eeeeee;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-gray > .panel-heading {
  color: #373e4a;
  background-color: #eeeeee;
  border-color: #ffffff;
}
.panel-gray > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #eeeeee;
}
.panel-gray > .panel-heading > .dropdown .caret {
  border-color: #373e4a transparent;
}
.panel-gray > .panel-heading > .panel-title > a {
  color: #373e4a;
}
.panel-gray > .panel-heading > .panel-options > a {
  display: inline-block;
  color: rgba(55, 62, 74, 0.7);
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gray > .panel-heading > .panel-options > a.bg {
  background-color: #e0e0e0;
  margin-left: 5px;
}
.panel-gray > .panel-heading > .panel-options > a.bg:hover {
  background-color: #e8e8e8;
}
.panel-gray > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #eeeeee;
  padding-top: 5px;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #eeeeee;
  background-color: #eeeeee;
  padding: 8px 10px;
  color: rgba(55, 62, 74, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #eeeeee;
  background-color: #eeeeee;
  color: #373e4a;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #373e4a;
}
.panel-gray > .panel-footer {
  background-color: #eeeeee;
  color: #373e4a;
  border-top-color: #ffffff;
}
.panel-gray > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #eeeeee;
}
.panel-gray > .panel-body + .panel-body {
  border-top-color: #ffffff;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li > a {
  background-color: #ffffff;
  border-bottom: 1px solid #ffffff;
}
.panel-gray > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  background-color: #eeeeee;
  border-color: #ffffff;
  border-bottom-color: #eeeeee;
}
.panel-gray > .panel-body {
  background-color: #eeeeee;
}
.panel-gray > .panel-body + .panel-body {
  border-top-color: #ffffff;
}
.panel-gradient {
  border-color: #cccccc;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.panel-gradient > .panel-heading {
  color: #303641;
  background-color: #ffffff;
  border-color: #cccccc;
}
.panel-gradient > .panel-heading + .panel-collapse .panel-body {
  border-top-color: #cccccc;
}
.panel-gradient > .panel-heading > .dropdown .caret {
  border-color: #303641 transparent;
}
.panel-gradient > .panel-heading > .panel-title > a {
  color: #303641;
}
.panel-gradient > .panel-heading > .panel-options > a {
  display: inline-block;
  color: rgba(48, 54, 65, 0.7);
  text-align: center;
  line-height: 1;
  padding: 4px 2px;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gradient > .panel-heading > .panel-options > a.bg {
  background-color: #d1d1d1;
  margin-left: 5px;
}
.panel-gradient > .panel-heading > .panel-options > a.bg:hover {
  background-color: #d9d9d9;
}
.panel-gradient > .panel-heading > .panel-options > a i {
  margin: 0;
  padding: 0;
  display: inline-block;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs {
  position: relative;
  top: 1px;
  border-bottom: 1px solid #cccccc;
  padding-top: 5px;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li {
  background-color: transparent;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li > a {
  border-color: #cccccc;
  background-color: #cccccc;
  padding: 8px 10px;
  color: rgba(48, 54, 65, 0.5);
  font-size: 12px;
  -webkit-border-radius: 3px 3px 0 0;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px 3px 0 0;
  -moz-background-clip: padding;
  border-radius: 3px 3px 0 0;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li > a > i {
  font-size: 14px;
  line-height: 1;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li.active > a {
  border-bottom: 1px solid #ffffff;
  background-color: #ffffff;
  color: #303641;
}
.panel-gradient > .panel-heading > .panel-options > .nav-tabs > li.active > a > i {
  color: #303641;
}
.panel-gradient > .panel-footer {
  background-color: #ffffff;
  color: #303641;
  border-top-color: #cccccc;
}
.panel-gradient > .panel-footer + .panel-collapse .panel-body {
  border-bottom-color: #cccccc;
}
.panel-gradient > .panel-body + .panel-body {
  border-top-color: #cccccc;
}
.panel-gradient > .panel-heading {
  background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiB2aWV3Qm94PSIwIDAgMSAxIiBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJub25lIj48bGluZWFyR3JhZGllbnQgaWQ9Imxlc3NoYXQtZ2VuZXJhdGVkIiBncmFkaWVudFVuaXRzPSJ1c2VyU3BhY2VPblVzZSIgeDE9IjAlIiB5MT0iMCUiIHgyPSIwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNmZmZmZmYiIHN0b3Atb3BhY2l0eT0iMSIvPjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iI2U1ZTVlNSIgc3RvcC1vcGFjaXR5PSIxIi8+PC9saW5lYXJHcmFkaWVudD48cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iMSIgaGVpZ2h0PSIxIiBmaWxsPSJ1cmwoI2xlc3NoYXQtZ2VuZXJhdGVkKSIgLz48L3N2Zz4=);
  background-image: -webkit-linear-gradient(top, #ffffff 0%, #e5e5e5 100%);
  background-image: -moz-linear-gradient(top, #ffffff 0%, #e5e5e5 100%);
  background-image: -o-linear-gradient(top, #ffffff 0%, #e5e5e5 100%);
  background-image: linear-gradient(to bottom, #ffffff 0%, #e5e5e5 100%);
}
.panel-gradient > .panel-heading > .panel-options i {
  color: #303641;
}
.well {
  min-height: 20px;
  padding: 19px;
  margin-bottom: 20px;
  background-color: #f5f5f6;
  border: 1px solid #e2e2e5;
  border-radius: 3px;
}
.well blockquote {
  border-color: #ddd;
  border-color: rgba(0, 0, 0, 0.15);
}
.well > h1 {
  margin-top: 5px;
}
.well-lg {
  padding: 24px;
  border-radius: 3px;
}
.well-sm {
  padding: 9px;
  border-radius: 2px;
}
.close {
  float: right;
  font-size: 18px;
  font-weight: bold;
  line-height: 1;
  color: #000000;
  -webkit-opacity: 0.2;
  -moz-opacity: 0.2;
  opacity: 0.2;
  filter: alpha(opacity=20);
}
.close:hover,
.close:focus {
  color: #000000;
  text-decoration: none;
  cursor: pointer;
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  filter: alpha(opacity=50);
}
button.close {
  padding: 0;
  cursor: pointer;
  background: transparent;
  border: 0;
  -webkit-appearance: none;
}
.modal-open {
  overflow: hidden;
}
.modal-open .page-container {
  padding-right: 15px;
}
.modal {
  display: none;
  overflow: auto;
  overflow-y: scroll;
  overflow-y: auto;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1050;
  -webkit-overflow-scrolling: touch;
  outline: 0;
}
.modal.custom-width {
  margin-left: auto;
  margin-right: auto;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.modal.custom-width .modal-dialog {
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.modal .modal-header .close {
  background: #f0f0f1;
  display: inline-block;
  padding: 0px 5px;
  line-height: 1;
  color: #787878;
  -webkit-opacity: 0.7;
  -moz-opacity: 0.7;
  opacity: 0.7;
  filter: alpha(opacity=70);
  text-shadow: none;
  font-weight: 300;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
  -moz-transition: all 300ms ease-in-out;
  -o-transition: all 300ms ease-in-out;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}
.modal .modal-header .close:hover {
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
}
.modal .modal-header h4 {
  text-shadow: none;
}
.modal .modal-body > p:last-child {
  margin-bottom: 0;
}
.modal.inline {
  display: block;
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
  position: relative;
  left: 0;
  top: 0;
  right: auto;
  bottom: auto;
  overflow: hidden;
  z-index: 0 !important;
}
.modal.inline .modal-dialog {
  margin: 0;
  width: auto;
}
.modal.invert .modal-dialog .modal-content {
  background-color: #303641;
  color: #aaabae;
  border-color: #454a54;
}
.modal.invert .modal-dialog .modal-content .modal-header {
  border-bottom-color: #454a54;
}
.modal.invert .modal-dialog .modal-content .modal-header .close {
  background-color: #454a54;
  color: #c7cad1;
}
.modal.invert .modal-dialog .modal-content .modal-header h4 {
  color: #ffffff;
}
.modal.invert .modal-dialog .modal-content .modal-footer {
  border-top-color: #454a54;
}
.modal.gray .modal-dialog .modal-content {
  background-color: #f0f0f1;
  border-color: #ffffff;
}
.modal.gray .modal-dialog .modal-content .modal-header {
  border-bottom-color: #ffffff;
}
.modal.gray .modal-dialog .modal-content .modal-header .close {
  background-color: #ffffff;
  color: #808080;
}
.modal.gray .modal-dialog .modal-content .modal-header h4 {
  color: #6b6b6b;
}
.modal.gray .modal-dialog .modal-content .modal-footer {
  border-top-color: #ffffff;
}
.modal.fade .modal-dialog {
  -webkit-transform: translateY(-400px) scale(1.1);
  -moz-transform: translateY(-400px) scale(1.1);
  -o-transform: translateY(-400px) scale(1.1);
  -ms-transform: translateY(-400px) scale(1.1);
  transform: translateY(-400px) scale(1.1);
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  -webkit-transition: -webkit-transform 0.5s cubic-bezier(0.175, 0.885, 0.320, 1.275);
  -moz-transition: -moz-transform 0.5s cubic-bezier(0.175, 0.885, 0.320, 1.275);
  -o-transition: -o-transform 0.5s cubic-bezier(0.175, 0.885, 0.320, 1.275);
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.320, 1.275);
}
.modal.in .modal-dialog {
  -moz-transform: translate(0, 0);
  -o-transform: translate(0, 0);
  -webkit-transform: translate(0, 0);
  -ms-transform: translate(0, 0);
  transform: translate(0, 0);
  -webkit-opacity: 1;
  -moz-opacity: 1;
  opacity: 1;
  filter: alpha(opacity=100);
}
.modal-dialog {
  position: relative;
  margin-left: auto;
  margin-right: auto;
  width: auto;
  padding: 10px;
}
.modal-dialog.fade {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.modal-dialog.in {
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.modal-content {
  position: relative;
  background-color: #ffffff;
  border: 1px solid #ebebeb;
  border-radius: 3px;
  -moz-box-shadow: none;
  -webkit-box-shadow: none;
  box-shadow: none;
  background-clip: padding-box;
  outline: none;
}
.modal-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1040;
  background-color: #000000;
}
.modal-backdrop.fade {
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.modal-backdrop.in {
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  filter: alpha(opacity=50);
}
.modal-header {
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
  min-height: 16.42857143px;
}
.modal-header .close {
  margin-top: -2px;
}
.modal-title {
  margin: 0;
  line-height: 1.42857143;
}
.modal-body {
  position: relative;
  padding: 20px;
}
.modal-footer {
  margin-top: 15px;
  padding: 19px 20px 20px;
  text-align: right;
  border-top: 1px solid #e5e5e5;
}
.modal-footer:before,
.modal-footer:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.modal-footer:after {
  clear: both;
}
.modal-footer:before,
.modal-footer:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.modal-footer:after {
  clear: both;
}
.modal-footer .btn + .btn {
  margin-left: 5px;
  margin-bottom: 0;
}
.modal-footer .btn-group .btn + .btn {
  margin-left: -1px;
}
.modal-footer .btn-block + .btn-block {
  margin-left: 0;
}
@media screen and (min-width: 768px) {
  .modal-dialog {
    width: 600px;
    padding-top: 30px;
    padding-bottom: 30px;
  }
  .modal-dialog.modal-lg {
    width: 900px;
  }
  .modal-dialog.modal-sm {
    width: 400px;
  }
}
.tooltip {
  position: absolute;
  z-index: 1030;
  display: block;
  visibility: visible;
  font-size: 11px;
  line-height: 1.4;
  -webkit-opacity: 0;
  -moz-opacity: 0;
  opacity: 0;
  filter: alpha(opacity=0);
}
.tooltip.in {
  -webkit-opacity: 0.9;
  -moz-opacity: 0.9;
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.tooltip.top {
  margin-top: -3px;
  padding: 5px 0;
}
.tooltip.right {
  margin-left: 3px;
  padding: 0 5px;
}
.tooltip.bottom {
  margin-top: 3px;
  padding: 5px 0;
}
.tooltip.left {
  margin-left: -3px;
  padding: 0 5px;
}
.tooltip.tooltip-primary .tooltip-inner {
  background-color: #303641;
  color: #ffffff;
}
.tooltip.tooltip-primary.top .tooltip-arrow {
  border-top-color: #303641;
}
.tooltip.tooltip-primary.top-left .tooltip-arrow {
  border-top-color: #303641;
}
.tooltip.tooltip-primary.top-right .tooltip-arrow {
  border-top-color: #303641;
}
.tooltip.tooltip-primary.right .tooltip-arrow {
  border-right-color: #303641;
}
.tooltip.tooltip-primary.left .tooltip-arrow {
  border-left-color: #303641;
}
.tooltip.tooltip-primary.bottom .tooltip-arrow {
  border-bottom-color: #303641;
}
.tooltip.tooltip-primary.bottom-left .tooltip-arrow {
  border-bottom-color: #303641;
}
.tooltip.tooltip-primary.bottom-right .tooltip-arrow {
  border-bottom-color: #303641;
}
.tooltip.tooltip-secondary .tooltip-inner {
  background-color: #ec5956;
  color: #ffffff;
}
.tooltip.tooltip-secondary.top .tooltip-arrow {
  border-top-color: #ec5956;
}
.tooltip.tooltip-secondary.top-left .tooltip-arrow {
  border-top-color: #ec5956;
}
.tooltip.tooltip-secondary.top-right .tooltip-arrow {
  border-top-color: #ec5956;
}
.tooltip.tooltip-secondary.right .tooltip-arrow {
  border-right-color: #ec5956;
}
.tooltip.tooltip-secondary.left .tooltip-arrow {
  border-left-color: #ec5956;
}
.tooltip.tooltip-secondary.bottom .tooltip-arrow {
  border-bottom-color: #ec5956;
}
.tooltip.tooltip-secondary.bottom-left .tooltip-arrow {
  border-bottom-color: #ec5956;
}
.tooltip.tooltip-secondary.bottom-right .tooltip-arrow {
  border-bottom-color: #ec5956;
}
.tooltip.tooltip-default .tooltip-inner {
  background-color: #ebebeb;
  color: #303641;
}
.tooltip.tooltip-default.top .tooltip-arrow {
  border-top-color: #ebebeb;
}
.tooltip.tooltip-default.top-left .tooltip-arrow {
  border-top-color: #ebebeb;
}
.tooltip.tooltip-default.top-right .tooltip-arrow {
  border-top-color: #ebebeb;
}
.tooltip.tooltip-default.right .tooltip-arrow {
  border-right-color: #ebebeb;
}
.tooltip.tooltip-default.left .tooltip-arrow {
  border-left-color: #ebebeb;
}
.tooltip.tooltip-default.bottom .tooltip-arrow {
  border-bottom-color: #ebebeb;
}
.tooltip.tooltip-default.bottom-left .tooltip-arrow {
  border-bottom-color: #ebebeb;
}
.tooltip.tooltip-default.bottom-right .tooltip-arrow {
  border-bottom-color: #ebebeb;
}
.tooltip-inner {
  max-width: 200px;
  padding: 3px 8px;
  color: #ffffff;
  text-align: center;
  text-decoration: none;
  background-color: #000000;
  border-radius: 3px;
}
.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.tooltip.top .tooltip-arrow {
  bottom: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 5px 5px 0;
  border-top-color: #000000;
}
.tooltip.top-left .tooltip-arrow {
  bottom: 0;
  left: 5px;
  border-width: 5px 5px 0;
  border-top-color: #000000;
}
.tooltip.top-right .tooltip-arrow {
  bottom: 0;
  right: 5px;
  border-width: 5px 5px 0;
  border-top-color: #000000;
}
.tooltip.right .tooltip-arrow {
  top: 50%;
  left: 0;
  margin-top: -5px;
  border-width: 5px 5px 5px 0;
  border-right-color: #000000;
}
.tooltip.left .tooltip-arrow {
  top: 50%;
  right: 0;
  margin-top: -5px;
  border-width: 5px 0 5px 5px;
  border-left-color: #000000;
}
.tooltip.bottom .tooltip-arrow {
  top: 0;
  left: 50%;
  margin-left: -5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000000;
}
.tooltip.bottom-left .tooltip-arrow {
  top: 0;
  left: 5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000000;
}
.tooltip.bottom-right .tooltip-arrow {
  top: 0;
  right: 5px;
  border-width: 0 5px 5px;
  border-bottom-color: #000000;
}
.popover {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1010;
  display: none;
  max-width: 276px;
  padding: 1px;
  text-align: left;
  background-color: #ffffff;
  background-clip: padding-box;
  border: 1px solid #cccccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  white-space: normal;
}
.popover.top {
  margin-top: -10px;
}
.popover.right {
  margin-left: 10px;
}
.popover.bottom {
  margin-top: 10px;
}
.popover.left {
  margin-left: -10px;
}
.popover.popover-primary {
  background-color: #303641;
  color: #ffffff;
  border-color: #303641;
  /*&.top .arrow { border-top-color: @popover_bg; &:after { border-top-color: @popover_border; } }
	&.bottom .arrow { border-top-color: @popover_bg; &:after { border-bottom-color: @popover_border; } }
	&.left .arrow { border-right-color: @popover_bg; &:after { border-left-color: @popover_border; } }
	&.right .arrow { border-left-color: @popover_bg; &:after { border-right-color: @popover_border; } }
	*/
}
.popover.popover-primary .popover-title {
  background-color: #3b4250;
  border-bottom-color: #303641;
  color: #ffffff;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.popover.popover-primary.top .arrow {
  border-top-color: #303641;
}
.popover.popover-primary.top .arrow:after {
  border-top-color: #303641;
}
.popover.popover-primary.bottom .arrow {
  border-bottom-color: #303641;
}
.popover.popover-primary.bottom .arrow:after {
  border-bottom-color: #303641;
}
.popover.popover-primary.left .arrow {
  border-left-color: #303641;
}
.popover.popover-primary.left .arrow:after {
  border-left-color: #303641;
}
.popover.popover-primary.right .arrow {
  border-right-color: #303641;
}
.popover.popover-primary.right .arrow:after {
  border-right-color: #303641;
}
.popover.popover-secondary {
  background-color: #ec5956;
  color: #ffffff;
  border-color: #ec5956;
  /*&.top .arrow { border-top-color: @popover_bg; &:after { border-top-color: @popover_border; } }
	&.bottom .arrow { border-top-color: @popover_bg; &:after { border-bottom-color: @popover_border; } }
	&.left .arrow { border-right-color: @popover_bg; &:after { border-left-color: @popover_border; } }
	&.right .arrow { border-left-color: @popover_bg; &:after { border-right-color: @popover_border; } }
	*/
}
.popover.popover-secondary .popover-title {
  background-color: #e9423f;
  border-bottom-color: #ec5956;
  color: #ffffff;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.popover.popover-secondary.top .arrow {
  border-top-color: #ec5956;
}
.popover.popover-secondary.top .arrow:after {
  border-top-color: #ec5956;
}
.popover.popover-secondary.bottom .arrow {
  border-bottom-color: #ec5956;
}
.popover.popover-secondary.bottom .arrow:after {
  border-bottom-color: #ec5956;
}
.popover.popover-secondary.left .arrow {
  border-left-color: #ec5956;
}
.popover.popover-secondary.left .arrow:after {
  border-left-color: #ec5956;
}
.popover.popover-secondary.right .arrow {
  border-right-color: #ec5956;
}
.popover.popover-secondary.right .arrow:after {
  border-right-color: #ec5956;
}
.popover.popover-default {
  background-color: #ebebeb;
  color: #373e4a;
  border-color: #ebebeb;
  /*&.top .arrow { border-top-color: @popover_bg; &:after { border-top-color: @popover_border; } }
	&.bottom .arrow { border-top-color: @popover_bg; &:after { border-bottom-color: @popover_border; } }
	&.left .arrow { border-right-color: @popover_bg; &:after { border-left-color: @popover_border; } }
	&.right .arrow { border-left-color: @popover_bg; &:after { border-right-color: @popover_border; } }
	*/
}
.popover.popover-default .popover-title {
  background-color: #d2d2d2;
  border-bottom-color: #ebebeb;
  color: #303641;
  -webkit-border-radius: 3px;
  -webkit-background-clip: padding-box;
  -moz-border-radius: 3px;
  -moz-background-clip: padding;
  border-radius: 3px;
  background-clip: padding-box;
}
.popover.popover-default.top .arrow {
  border-top-color: #ebebeb;
}
.popover.popover-default.top .arrow:after {
  border-top-color: #ebebeb;
}
.popover.popover-default.bottom .arrow {
  border-bottom-color: #ebebeb;
}
.popover.popover-default.bottom .arrow:after {
  border-bottom-color: #ebebeb;
}
.popover.popover-default.left .arrow {
  border-left-color: #ebebeb;
}
.popover.popover-default.left .arrow:after {
  border-left-color: #ebebeb;
}
.popover.popover-default.right .arrow {
  border-right-color: #ebebeb;
}
.popover.popover-default.right .arrow:after {
  border-right-color: #ebebeb;
}
.popover-title {
  margin: 0;
  padding: 8px 14px;
  font-size: 12px;
  font-weight: normal;
  line-height: 18px;
  background-color: #f7f7f7;
  border-bottom: 1px solid #ebebeb;
  border-radius: 5px 5px 0 0;
}
.popover-content {
  padding: 9px 14px;
}
.popover .arrow,
.popover .arrow:after {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
}
.popover .arrow {
  border-width: 11px;
}
.popover .arrow:after {
  border-width: 10px;
  content: "";
}
.popover.top .arrow {
  left: 50%;
  margin-left: -11px;
  border-bottom-width: 0;
  border-top-color: #999999;
  border-top-color: rgba(0, 0, 0, 0.25);
  bottom: -11px;
}
.popover.top .arrow:after {
  content: " ";
  bottom: 1px;
  margin-left: -10px;
  border-bottom-width: 0;
  border-top-color: #ffffff;
}
.popover.right .arrow {
  top: 50%;
  left: -11px;
  margin-top: -11px;
  border-left-width: 0;
  border-right-color: #999999;
  border-right-color: rgba(0, 0, 0, 0.25);
}
.popover.right .arrow:after {
  content: " ";
  left: 1px;
  bottom: -10px;
  border-left-width: 0;
  border-right-color: #ffffff;
}
.popover.bottom .arrow {
  left: 50%;
  margin-left: -11px;
  border-top-width: 0;
  border-bottom-color: #999999;
  border-bottom-color: rgba(0, 0, 0, 0.25);
  top: -11px;
}
.popover.bottom .arrow:after {
  content: " ";
  top: 1px;
  margin-left: -10px;
  border-top-width: 0;
  border-bottom-color: #ffffff;
}
.popover.left .arrow {
  top: 50%;
  right: -11px;
  margin-top: -11px;
  border-right-width: 0;
  border-left-color: #999999;
  border-left-color: rgba(0, 0, 0, 0.25);
}
.popover.left .arrow:after {
  content: " ";
  right: 1px;
  border-right-width: 0;
  border-left-color: #ffffff;
  bottom: -10px;
}
.carousel {
  position: relative;
}
.carousel-inner {
  position: relative;
  overflow: hidden;
  width: 100%;
}
.carousel-inner > .item {
  display: none;
  position: relative;
  min-width: 100%;
  -moz-transition: 0.6s ease-in-out left;
  -o-transition: 0.6s ease-in-out left;
  -webkit-transition: 0.6s ease-in-out left;
  transition: 0.6s ease-in-out left;
}
.carousel-inner > .item > img,
.carousel-inner > .item > a > img {
  display: block;
  max-width: 100%;
  height: auto;
  line-height: 1;
}
.carousel-inner > .active,
.carousel-inner > .next,
.carousel-inner > .prev {
  display: block;
}
.carousel-inner > .active {
  left: 0;
  right: 0;
}
.carousel-inner > .next,
.carousel-inner > .prev {
  position: absolute;
  top: 0;
  width: 100%;
}
.carousel-inner > .next {
  left: 100%;
}
.carousel-inner > .prev {
  left: -100%;
}
.carousel-inner > .next.left,
.carousel-inner > .prev.right {
  left: 0;
}
.carousel-inner > .active.left {
  left: -100%;
}
.carousel-inner > .active.right {
  left: 100%;
}
.carousel-control {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 15%;
  -webkit-opacity: 0.5;
  -moz-opacity: 0.5;
  opacity: 0.5;
  filter: alpha(opacity=50);
  font-size: 20px;
  color: #ffffff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}
.carousel-control.left {
  background-image: -webkit-gradient(linear, 0% top, 100% top, from(rgba(0, 0, 0, 0.5)), to(rgba(0, 0, 0, 0.0001)));
  background-image: -webkit-linear-gradient(left, color-stop(rgba(0, 0, 0, 0.5) 0%), color-stop(rgba(0, 0, 0, 0.0001) 100%));
  background-image: -moz-linear-gradient(left, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.0001) 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#80000000', endColorstr='#00000000', GradientType=1);
}
.carousel-control.right {
  left: auto;
  right: 0;
  background-image: -webkit-gradient(linear, 0% top, 100% top, from(rgba(0, 0, 0, 0.0001)), to(rgba(0, 0, 0, 0.5)));
  background-image: -webkit-linear-gradient(left, color-stop(rgba(0, 0, 0, 0.0001) 0%), color-stop(rgba(0, 0, 0, 0.5) 100%));
  background-image: -moz-linear-gradient(left, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  background-image: linear-gradient(to right, rgba(0, 0, 0, 0.0001) 0%, rgba(0, 0, 0, 0.5) 100%);
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#00000000', endColorstr='#80000000', GradientType=1);
}
.carousel-control:hover,
.carousel-control:focus {
  color: #ffffff;
  text-decoration: none;
  -webkit-opacity: 0.9;
  -moz-opacity: 0.9;
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.carousel-control .icon-prev,
.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-left,
.carousel-control .glyphicon-chevron-right {
  position: absolute;
  top: 50%;
  z-index: 5;
  display: inline-block;
}
.carousel-control .icon-prev,
.carousel-control .glyphicon-chevron-left {
  left: 50%;
}
.carousel-control .icon-next,
.carousel-control .glyphicon-chevron-right {
  right: 50%;
}
.carousel-control .icon-prev,
.carousel-control .icon-next {
  width: 20px;
  height: 20px;
  margin-top: -10px;
  margin-left: -10px;
  font-family: serif;
}
.carousel-control .icon-prev:before {
  content: '\2039';
}
.carousel-control .icon-next:before {
  content: '\203a';
}
.carousel-indicators {
  position: absolute;
  bottom: 10px;
  left: 50%;
  z-index: 15;
  width: 60%;
  margin-left: -30%;
  padding-left: 0;
  list-style: none;
  text-align: center;
}
.carousel-indicators li {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 1px;
  text-indent: -999px;
  border: 1px solid #ffffff;
  border-radius: 10px;
  cursor: pointer;
  background-color: #000 \9;
  background-color: rgba(0, 0, 0, 0);
}
.carousel-indicators .active {
  margin: 0;
  width: 12px;
  height: 12px;
  background-color: #ffffff;
}
.carousel-caption {
  position: absolute;
  left: 15%;
  right: 15%;
  bottom: 20px;
  z-index: 10;
  padding-top: 20px;
  padding-bottom: 20px;
  color: #ffffff;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
}
.carousel-caption .btn {
  text-shadow: none;
}
@media screen and (min-width: 768px) {
  .carousel-control .glyphicons-chevron-left,
  .carousel-control .glyphicons-chevron-right,
  .carousel-control .icon-prev,
  .carousel-control .icon-next {
    width: 30px;
    height: 30px;
    margin-top: -15px;
    margin-left: -15px;
    font-size: 30px;
  }
  .carousel-caption {
    left: 20%;
    right: 20%;
    padding-bottom: 30px;
  }
  .carousel-indicators {
    bottom: 20px;
  }
}
.clearfix:before,
.clearfix:after {
  content: " ";
  /* 1 */
  display: table;
  /* 2 */
}
.clearfix:after {
  clear: both;
}
.center-block {
  display: block;
  margin-left: auto;
  margin-right: auto;
}
.pull-right {
  float: right !important;
}
.pull-left {
  float: left !important;
}
.hide {
  display: none !important;
}
.show {
  display: block !important;
}
.invisible {
  visibility: hidden;
}
.text-hide {
  font: 0/0 a;
  color: transparent;
  text-shadow: none;
  background-color: transparent;
  border: 0;
}
.hidden {
  display: none !important;
  visibility: hidden !important;
}
.affix {
  position: fixed;
}
@-ms-viewport {
  width: device-width;
}
.visible-xs,
.visible-sm,
.visible-md,
.visible-lg,
tr.visible-xs,
tr.visible-sm,
tr.visible-md,
tr.visible-lg,
th.visible-xs,
th.visible-sm,
th.visible-md,
th.visible-lg,
td.visible-xs,
td.visible-sm,
td.visible-md,
td.visible-lg {
  display: none !important;
}
@media (max-width: 767px) {
  .visible-xs {
    display: block !important;
  }
  tr.visible-xs {
    display: table-row !important;
  }
  th.visible-xs,
  td.visible-xs {
    display: table-cell !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .visible-sm {
    display: block !important;
  }
  tr.visible-sm {
    display: table-row !important;
  }
  th.visible-sm,
  td.visible-sm {
    display: table-cell !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .visible-md {
    display: block !important;
  }
  tr.visible-md {
    display: table-row !important;
  }
  th.visible-md,
  td.visible-md {
    display: table-cell !important;
  }
}
@media (min-width: 1200px) {
  .visible-lg {
    display: block !important;
  }
  tr.visible-lg {
    display: table-row !important;
  }
  th.visible-lg,
  td.visible-lg {
    display: table-cell !important;
  }
}
@media (max-width: 767px) {
  .hidden-xs,
  tr.hidden-xs,
  th.hidden-xs,
  td.hidden-xs {
    display: none !important;
  }
}
@media (min-width: 768px) and (max-width: 991px) {
  .hidden-sm,
  tr.hidden-sm,
  th.hidden-sm,
  td.hidden-sm {
    display: none !important;
  }
}
@media (min-width: 992px) and (max-width: 1199px) {
  .hidden-md,
  tr.hidden-md,
  th.hidden-md,
  td.hidden-md {
    display: none !important;
  }
}
@media (min-width: 1200px) {
  .hidden-lg,
  tr.hidden-lg,
  th.hidden-lg,
  td.hidden-lg {
    display: none !important;
  }
}
.visible-print,
tr.visible-print,
th.visible-print,
td.visible-print {
  display: none !important;
}
@media print {
  .visible-print {
    display: block !important;
  }
  tr.visible-print {
    display: table-row !important;
  }
  th.visible-print,
  td.visible-print {
    display: table-cell !important;
  }
}
@media print {
  .hidden-print,
  tr.hidden-print,
  th.hidden-print,
  td.hidden-print {
    display: none !important;
  }
}
