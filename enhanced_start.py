import os
import webbrowser
import subprocess
import time
import signal
import sys
import psutil  # ��Ҫ��װ��pip install psutil

def kill_process_on_port(port):
    """ɱ��ռ��ָ���˿ڵĽ���"""
    for proc in psutil.process_iter(['pid', 'name', 'connections']):
        try:
            for conn in proc.connections():
                if conn.laddr.port == port:
                    print(f"����ռ�ö˿� {port} �Ľ���: {proc.pid}��������ֹ...")
                    if os.name == 'nt':  # Windows
                        subprocess.run(['taskkill', '/F', '/PID', str(proc.pid)], 
                                     stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                    else:  # Linux/Mac
                        os.kill(proc.pid, signal.SIGKILL)
                    return True
        except (psutil.AccessDenied, psutil.NoSuchProcess, AttributeError):
            pass
    return False

def register_exit_handler(processes):
    """ע���˳��������ȷ�����н����ڽű��˳�ʱ��ֹ"""
    def exit_handler():
        print("���ڹر����з���...")
        for proc in processes:
            if proc and proc.poll() is None:  # ������̻�������
                try:
                    proc.terminate()
                    proc.wait(timeout=3)
                except:
                    if proc.poll() is None:
                        proc.kill()
        # ȷ���˿�û�б�ռ��
        kill_process_on_port(8127)
        print("���з����ѹرա�")
    
    # ע��������
    import atexit
    atexit.register(exit_handler)

def main():
    print("=" * 60)
    print("   ��Դ���ݷ�����۸�Ԥ��ϵͳ - ��ǿ��������")
    print("=" * 60)
    
    # ��ȡ��ǰĿ¼
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir == "":
        current_dir = os.getcwd()
    
    # �ȼ�鲢ɱ��ռ�ö˿ڵĽ���
    kill_process_on_port(8127)
    
    # �������������Ľ���
    processes = []
    
    # �������������������У�
    try:
        print("���������������...")
        # �������ű��Ƿ����
        spider_script = os.path.join(current_dir, "spider", "run_spider.py")
        if os.path.exists(spider_script):
            spider_process = subprocess.Popen(
                [sys.executable, spider_script], 
                cwd=current_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            processes.append(spider_process)
            print("������������ɹ���")
        else:
            # ��������spiderĿ¼�µ��κ�Python�ļ�
            spider_dir = os.path.join(current_dir, "spider")
            if os.path.isdir(spider_dir):
                for file in os.listdir(spider_dir):
                    if file.endswith(".py"):
                        spider_process = subprocess.Popen(
                            [sys.executable, os.path.join(spider_dir, file)], 
                            cwd=current_dir,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.PIPE
                        )
                        processes.append(spider_process)
                        print(f"������� {file} �����ɹ���")
                        break
                else:
                    print("δ�ҵ�����ű��������������������")
            else:
                print("δ�ҵ�spiderĿ¼�������������������")
    except Exception as e:
        print(f"�����������ʧ��: {e}")
    
    # ����Django������
    print("\n��������Web������...")
    try:
        # ʹ�ò�����������̨�ķ�ʽ��������
        django_process = subprocess.Popen(
            [sys.executable, "manage.py", "runserver", "0.0.0.0:8127"],
            cwd=current_dir
        )
        processes.append(django_process)
        
        # ע���˳���������ȷ�����н����ڽű��˳�ʱ����ֹ
        register_exit_handler(processes)
        
        # �ȴ�����������
        print("�ȴ�����������...")
        time.sleep(3)
        
        # �������
        print("���ڴ������...")
        webbrowser.open("http://localhost:8127/app/login/")
        
        print("\nϵͳ�ѳɹ�����!")
        print("=" * 60)
        print("  ���ʵ�ַ: http://localhost:8127/app/login/")
        print("=" * 60)
        print("\n�뱣�ִ˴��ڿ������رմ��ڽ�ֹͣ���з���")
        print("��Ctrl+C����ֹͣϵͳ���˳�...")
        
        # ���ֽű����У�ֱ���û���Ctrl+C
        while True:
            # ���Django�Ƿ�������
            if django_process.poll() is not None:
                print("Web������ֹͣ���С�")
                break
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n�յ���ֹ�źţ�����ֹͣϵͳ...")
    except Exception as e:
        print(f"����ʧ��: {e}")
    finally:
        # ������̣���Ȼregister_exit_handler���ڽű��˳�ʱִ�У������쳣�������ʽ���ø���ȫ��
        for proc in processes:
            if proc and proc.poll() is None:
                try:
                    proc.terminate()
                    proc.wait(timeout=3)
                except:
                    if proc.poll() is None:
                        proc.kill()
        
        # ȷ���˿�û�б�ռ��
        kill_process_on_port(8127)
    
    print("ϵͳ����ȫ�˳���")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n�����û��ж�")
    except Exception as e:
        print(f"�������: {e}")
    # �˳�ʱ��ע����˳����������Զ����� 