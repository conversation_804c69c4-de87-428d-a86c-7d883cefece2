# Generated by Django 3.0 on 2025-03-09 19:21

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='House',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.Char<PERSON>ield(max_length=200, verbose_name='标题')),
                ('attention', models.PositiveIntegerField(default=0, verbose_name='关注人数')),
                ('community', models.CharField(max_length=50, verbose_name='小区')),
                ('location', models.CharField(max_length=30, verbose_name='位置')),
                ('city', models.CharField(max_length=10, verbose_name='城市')),
                ('house_type', models.CharField(max_length=20, verbose_name='房屋类型')),
                ('area', models.Char<PERSON>ield(max_length=20, verbose_name='面积')),
                ('unit_price', models.CharField(max_length=20, verbose_name='单价')),
                ('total_price', models.CharField(max_length=20, verbose_name='总价')),
                ('description', models.TextField(max_length=500, verbose_name='介绍')),
                ('detail_url', models.URLField(max_length=500, verbose_name='详情网址')),
                ('image_url', models.URLField(max_length=500, verbose_name='图片链接')),
            ],
            options={
                'verbose_name': '房源数据',
                'verbose_name_plural': '房源数据',
                'db_table': '房源数据',
            },
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='id')),
                ('username', models.CharField(default='', max_length=22, verbose_name='姓名')),
                ('password', models.CharField(default='', max_length=32, verbose_name='密码')),
                ('phone', models.CharField(default='', max_length=11, verbose_name='手机号')),
                ('email', models.CharField(default='', max_length=22, verbose_name='邮箱')),
                ('time', models.DateField(auto_now_add=True, verbose_name='创建时间')),
                ('avatar', models.FileField(default='user/avatar/default.gif', upload_to='user/avatar/', verbose_name='头像')),
            ],
            options={
                'verbose_name_plural': '用户管理',
                'db_table': 'User',
            },
        ),
        migrations.CreateModel(
            name='Histroy',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False, verbose_name='ID')),
                ('count', models.IntegerField(default=1, verbose_name='点击次数')),
                ('house', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.House')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='app.User')),
            ],
            options={
                'verbose_name_plural': '房源收藏',
                'db_table': 'History',
            },
        ),
    ]
