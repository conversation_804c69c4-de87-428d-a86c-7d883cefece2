#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
�Ľ���Django�����ű�
�Զ����Ҳ�����Django������������������������־��¼
"""

import os
import sys
import time
import signal
import socket
import subprocess
import platform
import logging
import datetime
import traceback
from pathlib import Path
import threading

# ȫ������
DEFAULT_PORT = 8127
MAX_RETRIES = 3
RETRY_DELAY = 2  # ��

# ������־
def setup_logging():
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = logs_dir / f"django_server_{timestamp}.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s [%(levelname)s] %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger('django_launcher')

logger = setup_logging()

def find_manage_py():
    """�ڵ�ǰĿ¼����Ŀ¼����Ŀ¼�в���manage.py�ļ�"""
    logger.info("���ڲ���Django��manage.py�ļ�...")
    
    # ��鵱ǰĿ¼
    if os.path.exists("manage.py"):
        logger.info("�ڵ�ǰĿ¼�ҵ�manage.py")
        return os.path.abspath("manage.py")
    
    # ��鸸Ŀ¼
    parent_dir = os.path.abspath(os.path.join(os.getcwd(), ".."))
    if os.path.exists(os.path.join(parent_dir, "manage.py")):
        logger.info(f"�ڸ�Ŀ¼�ҵ�manage.py: {parent_dir}")
        return os.path.join(parent_dir, "manage.py")
    
    # �����Ŀ¼
    for root, dirs, files in os.walk(os.getcwd()):
        if "manage.py" in files:
            manage_path = os.path.join(root, "manage.py")
            logger.info(f"����Ŀ¼�ҵ�manage.py: {root}")
            return manage_path
    
    logger.error("δ�ҵ�manage.py�ļ�")
    return None

def is_port_available(port):
    """���ָ���˿��Ƿ����"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.settimeout(1)
            result = s.connect_ex(('127.0.0.1', port))
            return result != 0
    except Exception as e:
        logger.error(f"���˿� {port} ������ʱ����: {e}")
        return False

def find_available_port(start_port=DEFAULT_PORT, max_attempts=10):
    """Ѱ�ҿ��ö˿�"""
    port = start_port
    attempts = 0
    
    while attempts < max_attempts:
        if is_port_available(port):
            logger.info(f"�ҵ����ö˿�: {port}")
            return port
        logger.warning(f"�˿� {port} ��ռ�ã�������һ���˿�")
        port += 1
        attempts += 1
    
    logger.error(f"�޷��ҵ����ö˿� (������ {start_port} �� {port-1})")
    return None

def kill_process_on_port(port):
    """������ֹռ��ָ���˿ڵĽ���"""
    try:
        logger.info(f"�����ͷŶ˿� {port}...")
        system = platform.system().lower()
        
        if system == "windows":
            # Windowsϵͳ
            cmd = f'for /f "tokens=5" %a in (\'netstat -ano ^| findstr :{port}\') do taskkill /F /PID %a'
            subprocess.run(cmd, shell=True, stderr=subprocess.PIPE, stdout=subprocess.PIPE)
        else:
            # Linux/Macϵͳ
            cmd = f"lsof -i :{port} | grep LISTEN | awk '{{print $2}}' | xargs -r kill -9"
            subprocess.run(cmd, shell=True, stderr=subprocess.PIPE, stdout=subprocess.PIPE)
        
        time.sleep(1)  # �ȴ�������ֹ
        
        if is_port_available(port):
            logger.info(f"�ɹ��ͷŶ˿� {port}")
            return True
        else:
            logger.warning(f"�޷��ͷŶ˿� {port}")
            return False
    except Exception as e:
        logger.error(f"�����ͷŶ˿�ʱ����: {e}")
        return False

def start_django_server(port=DEFAULT_PORT, attempts=0):
    """����Django������"""
    if attempts >= MAX_RETRIES:
        logger.error(f"�� {MAX_RETRIES} �γ��Ժ����޷�����������")
        return None
    
    manage_py = find_manage_py()
    if not manage_py:
        logger.error("�޷��ҵ�manage.py�ļ�����ȷ��������ȷ��Ŀ¼��")
        return None
    
    # ���˿��Ƿ����
    if not is_port_available(port):
        logger.warning(f"�˿� {port} ��ռ��")
        if not kill_process_on_port(port):
            new_port = find_available_port(port + 1)
            if new_port:
                logger.info(f"������ʹ���¶˿�: {new_port}")
                port = new_port
            else:
                logger.error("�޷��ҵ����ö˿�")
                return None
    
    # ��������
    work_dir = os.path.dirname(manage_py)
    cmd = [sys.executable, "manage.py", "runserver", f"0.0.0.0:{port}"]
    
    logger.info(f"����Django���������˿�: {port}������Ŀ¼: {work_dir}")
    
    try:
        process = subprocess.Popen(
            cmd,
            cwd=work_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='replace'
        )
        
        # �ȴ�����������
        time.sleep(2)
        
        # �������Ƿ���������
        if process.poll() is not None:
            returncode = process.poll()
            out, err = process.communicate()
            logger.error(f"Django����������ʧ�ܣ��˳���: {returncode}")
            if err:
                logger.error(f"������Ϣ: {err}")
            
            # �����ٴ�����
            logger.info(f"�ȴ� {RETRY_DELAY} �������...")
            time.sleep(RETRY_DELAY)
            return start_django_server(port, attempts + 1)
        
        logger.info(f"Django�����������ɹ����˿�: {port}")
        return process
        
    except Exception as e:
        logger.error(f"����Django������ʱ�����쳣: {e}")
        logger.error(traceback.format_exc())
        return None

def monitor_process(process):
    """��ؽ��̲���¼���"""
    def read_output(stream, log_func):
        for line in iter(stream.readline, ''):
            if line:
                log_func(line.strip())
    
    stdout_thread = threading.Thread(target=read_output, args=(process.stdout, logger.info))
    stderr_thread = threading.Thread(target=read_output, args=(process.stderr, logger.error))
    
    stdout_thread.daemon = True
    stderr_thread.daemon = True
    
    stdout_thread.start()
    stderr_thread.start()
    
    try:
        print("\n" + "="*50)
        print(f"�������������У��� Ctrl+C ֹͣ")
        print("="*50 + "\n")
        
        process.wait()
    except KeyboardInterrupt:
        logger.info("���յ���ֹ�źţ����ڹرշ�����...")
        if platform.system().lower() == "windows":
            process.send_signal(signal.CTRL_C_EVENT)
        else:
            process.send_signal(signal.SIGINT)
        
        # �ȴ�����������ֹ
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            logger.warning("������û�м�ʱ��Ӧ��ֹ�źţ�ǿ����ֹ")
            process.kill()
        
        logger.info("�������ѹر�")
    
    return process.returncode

def main():
    """������"""
    try:
        logger.info("="*50)
        logger.info("Django������������")
        logger.info("="*50)
        
        # ����������
        server_process = start_django_server()
        
        if server_process:
            return_code = monitor_process(server_process)
            logger.info(f"�������������˳���״̬��: {return_code}")
            return return_code
        else:
            logger.error("����������ʧ��")
            return 1
    
    except Exception as e:
        logger.error(f"����ִ�й����з���δ�����쳣: {e}")
        logger.error(traceback.format_exc())
        return 1

if __name__ == "__main__":
    sys.exit(main()) 