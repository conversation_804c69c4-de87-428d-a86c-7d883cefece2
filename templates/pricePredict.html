<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Neon Admin Panel">
    <meta name="author" content="">
    <title>价钱影响</title>
    <link rel="icon" href="/static/picture/mapview.png" type="image/x-icon">
    <link rel="stylesheet" href="/static/css/jquery-ui-1.10.3.custom.min.css">
    <link rel="stylesheet" href="/static/css/entypo.css">
    <link rel="stylesheet" href="/static/css/css.css">
    <link rel="stylesheet" href="/static/css/bootstrap.css">
    <link rel="stylesheet" href="/static/css/neon-core.css">
    <link rel="stylesheet" href="/static/css/neon-theme.css">
    <link rel="stylesheet" href="/static/css/neon-forms.css">
    <link rel="stylesheet" href="/static/css/custom.css">

    <script src="/static/js/jquery-1.11.0.min.js"></script>
    <link rel="stylesheet" href="/static/css/font-awesome.min.css">
</head>
<body class="page-body" data-url="http://neon.dev">

<div class="page-container">
    <div class="sidebar-menu">
        <header class="logo-env">
            <div class="logo" style="text-align:center">
                <a href="/app/index/">
                    <h3 style="color: #fff;font-weight: bold;margin-top: 5px;">广州市租房数据分析</h3>

                </a>
            </div>
        </header>

        <div class="sidebar-user-info">

            <div class="sui-normal">
                <a href="#" class="user-link">
                    <img style="width:95%" src=/media/{{ useravatar }} alt="" class="img-circle">

                    <span style="text-align:center;padding-top:209px">欢迎回来</span>
                    <strong style="text-align:center;margin-top:5px">{{ username }}</strong>
                </a>
            </div>

        </div>

        <ul id="main-menu" class="">
            <li>
                <a href="/app/index/">
                    <i class="entypo-gauge"></i>
                    <span>首页</span>
                </a>
            </li>

            <li>
                <a href="/app/selfInfo/">
                    <i class="entypo-user"></i>
                    <span>个人中心</span>
                </a>
            </li>
            <li>
                <a href="#">
                    <i class="entypo-layout"></i>
                    <span>数据统计</span>
                </a>
                <ul>
                    <li>
                        <a href="/app/tableData">
                            <i class="icon-tasks"></i>
                            <span>数据总览</span>
                        </a>
                    </li>
                    <li>
                        <a href="/app/historyTableData/">
                            <i class="icon-star"></i>
                            <span>房源收藏</span>
                        </a>
                    </li>

                </ul>
            </li>
            <li>
                <a href="#">
                    <i class="entypo-chart-bar"></i>
                    <span>可视化图表</span>
                </a>
                <ul>
                    <li>
                        <a href="/app/houseDistribute/">
                            <i class="entypo-light-down"></i>
                            <span>房源分布</span>
                        </a>
                    </li>
                    <li>
                        <a href="/app/typeincity/">
                            <i class="entypo-feather"></i>
                            <span>户型占比</span>
                        </a>
                    </li>
                    <li>
                        <a href="/app/housewordcloud/">
                            <i class="entypo-lamp"></i>
                            <span>词云汇总</span>
                        </a>
                    </li>
                    <li>
                        <a href="/app/housetyperank/">
                            <i class="entypo-graduation-cap"></i>
                            <span>类型级别</span>
                        </a>
                    </li>
                    <li class="active">
                        <a href="/app/servicemoney/">
                            <i class="entypo-network"></i>
                            <span>价钱影响</span>
                        </a>
                    </li>


                </ul>
            </li>
            <li class="opened active">
                <a href="/app/predict-all-prices/">
                    <i class="icon-bar-chart"></i>
                    <span>房价预测</span>
                </a>
            </li>
        </ul>

    </div>
    <div class="main-content">
        <div class="row">
            <!-- Raw Links -->
            <div style="display:flex;" class="col-md-12 hidden-xs">

                <ol class="breadcrumb bc-3">
                    <li>
                        <a href="#"><i class="entypo-home"></i>可视化图表</a>
                    </li>
                    <li class="active">
                        <strong>不同房源不同城市的价格预测</strong>
                    </li>
                </ol>
                <ul style="margin-left:auto" class="list-inline links-list pull-right">

                    <li class="sep"></li>
                    <li>
                        <a href="/app/logOut">
                            退出登录 <i class="entypo-logout right"></i>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <h2>房源优势的金额影响</h2>
        <br>
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <div class="panel-title">各类型在各个行政区的价格预测</div>
                        <div class="panel-options">
                            <a href="#" data-rel="collapse"><i class="entypo-down-open"></i></a>
                            <a href="#" data-rel="reload"><i class="entypo-arrows-ccw"></i></a>
                        </div>
                    </div>
                    <div class="panel-body text-center">
                        <div id="mainOne" style="width:100%;height:650px"></div>
                    </div>
                </div>
            </div>
        </div>
        <footer class="main">
            Copyright &copy; 2025. Python租房房源数据可视化分析 <a target="_blank"
                                                                   href="https://hz.lianjia.com/">链家网</a>
        </footer>
    </div>

</div>

<!-- Bottom Scripts -->
<script src="/static/js/main-gsap.js"></script>
<script src="/static/js/jquery-ui-1.10.3.minimal.min.js"></script>
<script src="/static/js/bootstrap.js"></script>
<script src="/static/js/joinable.js"></script>
<script src="/static/js/resizeable.js"></script>
<script src="/static/js/fileinput.js"></script>
<script src="/static/js/neon-api.js"></script>
<script src="/static/js/neon-chat.js"></script>
<script src="/static/js/neon-custom.js"></script>
<script src="/static/js/neon-demo.js"></script>
<script src="/static/js/echarts.js"></script>
<script>
    var chartDom = document.getElementById('mainOne');
    var myChart = echarts.init(chartDom);
    var option;
option = {
  title: {
    text: 'Stacked Area Chart'
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    }
  },
  legend: {
    data: {{ types|safe }}
  },
  toolbox: {
    feature: {
      saveAsImage: {}
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      data: {{ cities|safe }}
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: '合租',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      data: {{ predictions.合租 }}
    },
    {
      name: '整租',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      data: {{ predictions.整租 }}
    },
    {
      name: '独栋',
      type: 'line',
      stack: 'Total',
      areaStyle: {},
      emphasis: {
        focus: 'series'
      },
      data: {{ predictions.独栋 }}
    }
  ]
};

    option && myChart.setOption(option);
</script>
</body>
</html>