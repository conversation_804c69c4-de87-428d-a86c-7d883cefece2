{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Neon Admin Panel">
    <meta name="author" content="">
    <title>数据总览</title>
    <meta name="referrer" content="no-referrer">
    <link rel="icon" href="/static/picture/mapview.png" type="image/x-icon">
    <link rel="stylesheet" href="/static/css/jquery-ui-1.10.3.custom.min.css">
    <link rel="stylesheet" href="/static/css/entypo.css">
    <link rel="stylesheet" href="/static/css/css.css">
    <link rel="stylesheet" href="/static/css/bootstrap.css">
    <link rel="stylesheet" href="/static/css/neon-core.css">
    <link rel="stylesheet" href="/static/css/neon-theme.css">
    <link rel="stylesheet" href="/static/css/neon-forms.css">
    <link rel="stylesheet" href="/static/css/custom.css">
    <link rel="stylesheet" href="/static/css/font-awesome.min.css">
    <script src="/static/js/jquery-1.11.0.min.js"></script>
</head>
<style>
    body1 {
        font-family: Arial, sans-serif;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
        background-color: #f0f0f0;
    }

    .carousel-container {
        position: relative;
        width: 100%;
        max-width: 1200px;
        overflow: hidden;
        border: 2px solid #ddd;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .carousel-slide {
        display: flex;
        transition: transform 0.5s ease-in-out;
    }

    .carousel-slide img {
        width: 100%;
        height: auto;
    }

    button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        border: none;
        padding: 10px;
        cursor: pointer;
        border-radius: 50%;
        user-select: none;
    }

    button:focus {
        outline: none;
    }

    .prev {
        left: 10px;
    }

    .next {
        right: 10px;
    }
</style>
<body class="page-body  page-left-in" data-url="http://neon.dev">
<div class="page-container">
    <div class="sidebar-menu">
        <header class="logo-env">

            <div class="logo" style="text-align:center">
                <a href="/app/index/">
                    <h3 style="color: #fff;font-weight: bold;margin-top: 5px;">广州市租房数据分析</h3>

                </a>
            </div>


        </header>
        <div class="sidebar-user-info">
            <div class="sui-normal">
                <a href="#" class="user-link">
                    <img style="width:95%" src=/media/{{ request.session.username.avatar | safe }} class="img-circle">

                    <span style="text-align:center;padding-top:209px">欢迎回来</span>
                    <strong style="text-align:center;margin-top:5px">{{ username }}</strong>
                </a>
            </div>

        </div>


        <ul id="main-menu" class="">
            <li>
                <a href="/app/index/">
                    <i class="entypo-gauge"></i>
                    <span>首页</span>
                </a>
            </li>


            <li>
                <a href="/app/selfInfo/">
                    <i class="entypo-user"></i>
                    <span>个人中心</span>
                </a>
            </li>
            <li class="opened active">
                <ul>
                    <li class="active">
                        <a href="/app/tableData">
                            <i class="icon-tasks"></i>
                            <span>数据总览</span>
                        </a>
                    </li>
                    <li>
                        <a href="/app/historyTableData/">
                            <i class="icon-star"></i>
                            <span>房源收藏</span>
                        </a>
                    </li>

                </ul>
            </li>
            <li>
                <a href="#">
                    <i class="entypo-chart-bar"></i>
                    <span>可视化图表</span>
                </a>
                <ul>
                    <li>
                        <a href="/app/houseDistribute/">
                            <i class="entypo-light-down"></i>
                            <span>房源分布</span>
                        </a>
                    </li>
                    <li>
                        <a href="/app/typeincity/">
                            <i class="entypo-feather"></i>
                            <span>户型占比</span>
                        </a>
                    </li>
                    <li>
                        <a href="/app/housewordcloud">
                            <i class="entypo-lamp"></i>
                            <span>词云汇总</span>
                        </a>
                    </li>
                    <li>
                        <a href="/app/housetyperank/">
                            <i class="entypo-graduation-cap"></i>
                            <span>类型级别</span>
                        </a>
                    </li>
                    <li>
                        <a href="/app/servicemoney/">
                            <i class="entypo-network"></i>
                            <span>价钱影响 </span>
                        </a>
                    </li>

                </ul>
            </li>
                        <li>
                <a href="/app/predict-all-prices/">
                    <i class="icon-bar-chart"></i>
                    <span>房价预测</span>
                </a>
            </li>
        </ul>

    </div>
    <div class="main-content">

        <div class="row">
            <!-- Raw Links -->
            <div style="display:flex;" class="col-md-12 hidden-xs">

                <ul style="margin-left:auto" class="list-inline links-list pull-right">

                    <li class="sep"></li>

                    <li>
                        <a href="/app/logOut">
                            退出登录 <i class="entypo-logout right"></i>
                        </a>
                    </li>
                </ul>
            </div>

        </div>

        <hr style="margin-top:0">

        <div class="row">
            <div class="col-sm-12">
                <div class="panel panel-default" data-collapsed="0"><!-- to apply shadow add class "panel-shadow" -->
                    <!-- panel head -->
                    <div class="panel-heading">
                        <div class="panel-title">数据表格</div>

                        <div class="panel-options">
                            <a href="#" data-rel="collapse"><i class="entypo-down-open"></i></a>
                            <a href="#" data-rel="reload"><i class="entypo-arrows-ccw"></i></a>
                        </div>
                    </div>
                    <div class="panel-body">
                        <table class="table table-bordered datatable" id="table-1">
                            <thead>
                            <tr>
                                <th>编号</th>
                                <th>图片</th>
                                <th>房源名称</th>
                                <th>房源类型</th>
                                <th>房源布局/地址</th>
                                <th>标签</th>
                                <th>行政区</th>
                                <th>街道</th>
                                <th>房源面积</th>
                                <th>朝向</th>
                                <th>价钱</th>
                                <th>跳转</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for p in houses %}
                                <tr class="gradeU">
                                    <td>{{ forloop.counter }}</td>
                                    <td class="center">
                                        <img src="{{ p.img }}" alt="">
                                    </td>
                                    <td class="center">{{ p.title }}</td>
                                    <td class="center">{{ p.type }}</td>
                                    <td class="center">{{ p.building }}</td>
                                    <td class="center">{{ p.tag }}</td>
                                    <td class="center">{{ p.city }}</td>
                                    <td class="center">{{ p.street }}</td>
                                    <td class="center">{{ p.area }}㎡</td>
                                    <td class="center">{{ p.direct }}</td>
                                    <td class="center">{{ p.price }}/月</td>
                                    <td class="center text-center">
                                        <a target="_blank" href="https://qd.lianjia.com/{{ p.link }}"
                                                class="btn btn-info">房源详情</a>

                                        <a target="__blank" href="/app/addHistory/{{ p.id }}"
                                           class="btn btn-danger">收藏房源</a>
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>

                    </div>
                </div>

            </div>
        </div>
        <footer class="main">
            Copyright &copy; 2025. Python租房房源数据可视化分析 <a target="_blank"
                                                                   href="https://hz.lianjia.com/">链家网</a>
        </footer>

    </div>


</div>

<script src="/static/js/main-gsap.js"></script>
<script src="/static/js/jquery-ui-1.10.3.minimal.min.js"></script>
<script src="/static/js/bootstrap.js"></script>
<script src="/static/js/resizeable.js"></script>
<script src="/static/js/neon-api.js"></script>
<script src="/static/js/jquery.sparkline.min.js"></script>
<script src="/static/js/d3.v3.js"></script>
<script src="/static/js/rickshaw.min.js"></script>
<script src="/static/js/raphael-min.js"></script>
<script src="/static/js/morris.min.js"></script>
<script src="/static/js/toastr.js"></script>
<script src="/static/js/neon-chat.js"></script>
<script src="/static/js/neon-custom.js"></script>
<script src="/static/js/neon-demo.js"></script>
<link rel="stylesheet" href="/static/css/datatables.responsive.css">
<script src="/static/js/datatables.responsive.js"></script>
<script src="/static/js/jquery.dataTables.columnFilter.js"></script>
<script src="/static/js/jquery.dataTables.min.js"></script>
<script src="/static/js/TableTools.min.js"></script>
<script src="/static/js/dataTables.bootstrap.js"></script>
<script src="/static/js/lodash.min.js"></script>
</body>
<script src="/static/js/echarts.js"></script>
<script type="text/javascript">
    var responsiveHelper;
    var breakpointDefinition = {
        tablet: 1024,
        phone: 480
    };
    var tableContainer;

    jQuery(document).ready(function ($) {
        tableContainer = $("#table-1");

        tableContainer.dataTable({
            "sPaginationType": "bootstrap",
            "aLengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "bStateSave": true,


            "aoColumnDefs": [
                { 
                    "aTargets": [10],
                    "sType": "numeric", 
                    "mRender": function(data, type, full) {
                        if (type === 'sort') {
                            return parseFloat(data.replace(/[^0-9.]/g, ''));
                        }
                        return data;
                    }
                }
            ],

            bAutoWidth: false,
            fnPreDrawCallback: function () {
                if (!responsiveHelper) {
                    responsiveHelper = new ResponsiveDatatablesHelper(tableContainer, breakpointDefinition);
                }
            },
            fnRowCallback: function (nRow, aData, iDisplayIndex, iDisplayIndexFull) {
                responsiveHelper.createExpandIcon(nRow);
            },
            fnDrawCallback: function (oSettings) {
                responsiveHelper.respond();
            }
        });

        $(".dataTables_wrapper select").select2({
            minimumResultsForSearch: -1
        });
    });
</script>
<script>
    var chartDom = document.getElementById('main');
    var myChart = echarts.init(chartDom);
    var option;
    option = {
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            left: 'center'
        },
        series: [
            {
                name: '用户创建时间',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['50%', '60%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: '20',
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: {{ userTime | safe }}
            }
        ]
    };
    option && myChart.setOption(option);

</script>
<script src="script.js"></script>
<script>
    let currentIndex = 0;

    function showSlide(index) {
        const slides = document.querySelectorAll('.carousel-slide img');
        if (index >= slides.length) {
            currentIndex = 0;
        } else if (index < 0) {
            currentIndex = slides.length - 1;
        } else {
            currentIndex = index;
        }
        const offset = -currentIndex * 100 + '%';
        document.querySelector('.carousel-slide').style.transform = `translateX(${offset})`;
    }

    function nextSlide() {
        showSlide(currentIndex + 1);
    }

    function prevSlide() {
        showSlide(currentIndex - 1);
    }

    // 自动播放轮播图（可选）
    setInterval(nextSlide, 3000); // 每3秒切换一次
</script>
<!--
元数据数据csript代码
-->
</html>