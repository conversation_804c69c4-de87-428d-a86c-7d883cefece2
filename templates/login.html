<html lang="zh-CN">
{% load static %}
<head>
    <meta charset="utf-8">
    <link rel="icon" type="image/png" sizes="144x144" href="/static/picture/mapview.png"/>
    <title>链家网新房数据分析</title>
    <link href="{% static 'bootstrap/css/bootstrap.css' %}" rel="stylesheet">
    <link href="/static/bootstrap/css/bootstrap.css" rel="stylesheet">
    <meta name="theme-color" content="#7952b3">
    <style>
        .bd-placeholder-img {
            font-size: 1.125rem;
            text-anchor: middle;
            -webkit-user-select: none;
            -moz-user-select: none;
            user-select: none;
        }

        @media (min-width: 768px) {
            .bd-placeholder-img-lg {
                font-size: 3.5rem;
            }
        }

        .b-example-divider {
            height: 3rem;
            background-color: rgba(0, 0, 0, .1);
            border: solid rgba(0, 0, 0, .15);
            border-width: 1px 0;
            box-shadow: inset 0 .5em 1.5em rgba(0, 0, 0, .1), inset 0 .125em .5em rgba(0, 0, 0, .15);
        }

        .b-example-vr {
            flex-shrink: 0;
            width: 1.5rem;
            height: 100vh;
        }

        .bi {
            vertical-align: -.125em;
            fill: currentColor;
        }

        html,
        body {
            height: 100%;
        }

        body {
            display: flex;
            align-items: center;
            padding-top: 40px;
            padding-bottom: 40px;
            background-color: #f5f5f5;
            background-image: url("/static/picture/background.jpg");
            background-size: cover; /* 确保背景覆盖整个页面 */
            background-position: center; /* 背景图片居中 */
            height: 100vh; /* 设置高度为视口高度 */
            margin: 0; /* 去掉默认的边距 */
        }

        .form-signin {
            width: 100%;
            max-width: 400px;
            padding: 15px;
            margin: auto;
        }

        .form-signin .checkbox {
            font-weight: 400;
        }

        .form-signin .form-floating:focus-within {
            z-index: 2;
        }

        .form-signin input[type="email"] {
            margin-bottom: -1px;
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }

        .form-signin input[type="password"] {
            margin-bottom: 10px;
            border-top-left-radius: 0;
            border-top-right-radius: 0;
        }
    </style>
</head>
<body class="text-center">

<main class="form-signin">
    <form action="/app/login/" method="post">
        {% csrf_token %}
        <img class="mb-4" src="/static/picture/mapview.png" alt="" width="72" height="72">
        <h1 class="h3 mb-3 fw-normal">用户登录</h1>

        <div class="form-floating">
            <p class="text-center text-danger h4">{{ msg }}</p>
            <input type="text" required name="name" class="form-control" id="floatingInput" placeholder="<EMAIL>">
            <label for="floatingInput">用户名</label>
        </div>
        <div class="form-floating">
            <input type="password" required name="password" class="form-control" id="floatingPassword" placeholder="Password">
            <label for="floatingPassword">密码</label>
        </div>

        <div class="checkbox mb-3 text-start">
            <label>
                <input type="checkbox" value="remember-me"> 记住我
            </label>
        </div>
        <button class="w-100 btn btn-lg btn-primary" type="submit">登录</button>
        <a href="/app/register/" class="w-100 btn btn-lg btn-success mt-3">注册</a>
        <p class="mt-5 mb-3 text-muted">© 2023-小熊Coding</p>
    </form>
</main>
<script src="{% static 'bootstrap/js/bootstrap.bundle.min.js' %}"></script>

</body>
</html>