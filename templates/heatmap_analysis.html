<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="��������ͼ����">
    <meta name="author" content="">
    <title>��������ͼ����</title>
    <link rel="icon" href="/static/picture/mapview.png" type="image/x-icon">
    <link rel="stylesheet" href="/static/css/bootstrap.css">
    <link rel="stylesheet" href="/static/css/neon-core.css">
    <link rel="stylesheet" href="/static/css/neon-theme.css">
    <link rel="stylesheet" href="/static/css/entypo.css">
    <link rel="stylesheet" href="/static/css/custom.css">
    <script src="/static/js/jquery-1.11.0.min.js"></script>
</head>
<body class="page-body">

<div class="page-container">
    <div class="sidebar-menu">
        <header class="logo-env">
            <div class="logo" style="text-align:center">
                <a href="/app/index/">
                    <h3 style="color: #fff;font-weight: bold;margin-top: 5px;">�������ⷿ���ݷ���</h3>
                </a>
            </div>
        </header>

        <div class="sidebar-user-info">
            <div class="sui-normal">
                <a href="#" class="user-link">
                    <img style="width:95%" src=/media/{{ useravatar }} alt="" class="img-circle">
                    <span style="text-align:center;padding-top:209px">��ӭ����</span>
                    <strong style="text-align:center;margin-top:5px">{{ username }}</strong>
                </a>
            </div>
        </div>

        <ul id="main-menu" class="">
            <li><a href="/app/index/"><i class="entypo-gauge"></i><span>��ҳ</span></a></li>
            <li><a href="/app/selfInfo/"><i class="entypo-user"></i><span>��������</span></a></li>
            <li>
                <a href="#"><i class="entypo-layout"></i><span>����ͳ��</span></a>
                <ul>
                    <li><a href="/app/tableData"><i class="icon-tasks"></i><span>��������</span></a></li>
                    <li><a href="/app/historyTableData/"><i class="icon-star"></i><span>��Դ�ղ�</span></a></li>
                </ul>
            </li>
            <li class="opened active">
                <a href="#"><i class="entypo-chart-bar"></i><span>���ӻ�ͼ��</span></a>
                <ul>
                    <li><a href="/app/houseDistribute/"><i class="entypo-light-down"></i><span>��Դ�ֲ�</span></a></li>
                    <li><a href="/app/typeincity/"><i class="entypo-feather"></i><span>����ռ��</span></a></li>
                    <li><a href="/app/housewordcloud/"><i class="entypo-lamp"></i><span>���ƻ���</span></a></li>
                    <li><a href="/app/housetyperank/"><i class="entypo-graduation-cap"></i><span>���ͼ���</span></a></li>
                    <li><a href="/app/servicemoney/"><i class="entypo-network"></i><span>��ǮӰ��</span></a></li>
                    <li class="active"><a href="/app/heatmap-analysis/"><i class="entypo-chart-pie"></i><span>����ͼ����</span></a></li>
                </ul>
            </li>
            <li><a href="/app/predict-all-prices/"><i class="icon-bar-chart"></i><span>����Ԥ��</span></a></li>
        </ul>
    </div>
    
    <div class="main-content">
        <div class="row">
            <div style="display:flex;" class="col-md-12 hidden-xs">
                <ol class="breadcrumb bc-3">
                    <li><a href="#"><i class="entypo-home"></i>���ӻ�ͼ��</a></li>
                    <li class="active"><strong>����Ӱ����������ͼ����</strong></li>
                </ol>
                <ul style="margin-left:auto" class="list-inline links-list pull-right">
                    <li class="sep"></li>
                    <li><a href="/app/logOut">�˳���¼ <i class="entypo-logout right"></i></a></li>
                </ul>
            </div>
        </div>

        <h2>����Ӱ����������ͼ����</h2>
        <br>

        <!-- �����뻧�ͶԼ۸��Ӱ�� -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <div class="panel-title">�����뻧�ͶԷ��۵�Ӱ������ͼ</div>
                    </div>
                    <div class="panel-body">
                        <div id="cityTypeHeatmap" style="width:100%;height:500px"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- ����볯��Լ۸��Ӱ�� -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <div class="panel-title">����볯��Է��۵�Ӱ������ͼ</div>
                    </div>
                    <div class="panel-body">
                        <div id="areaDirectHeatmap" style="width:100%;height:500px"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- �۸������ɢ��ͼ -->
        <div class="row">
            <div class="col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <div class="panel-title">�����۸�Ĺ�ϵɢ��ͼ</div>
                    </div>
                    <div class="panel-body">
                        <div id="areaPriceScatter" style="width:100%;height:500px"></div>
                    </div>
                </div>
            </div>
        </div>

        <footer class="main">
            Copyright &copy; 2025. Python�ⷿ��Դ���ݿ��ӻ����� <a target="_blank" href="https://hz.lianjia.com/">������</a>
        </footer>
    </div>
</div>

<!-- Scripts -->
<script src="/static/js/bootstrap.js"></script>
<script src="/static/js/echarts.js"></script>
<script>
// �����뻧�ͶԼ۸��Ӱ������ͼ
var cityTypeChart = echarts.init(document.getElementById('cityTypeHeatmap'));
var cityTypeOption = {
    title: {
        text: '�����뻧�ͶԷ��۵�Ӱ������ͼ',
        left: 'center'
    },
    tooltip: {
        position: 'top'
    },
    grid: {
        height: '70%',
        top: '15%'
    },
    xAxis: {
        type: 'category',
        data: {{ cities|safe }},
        splitArea: {
            show: true
        },
        axisLabel: {
            interval: 0,
            rotate: 45
        },
        name: '����',
        nameLocation: 'middle',
        nameGap: 30
    },
    yAxis: {
        type: 'category',
        data: {{ types|safe }},
        splitArea: {
            show: true
        },
        name: '����',
        nameLocation: 'middle',
        nameGap: 30
    },
    visualMap: {
        min: 0,
        max: 5000,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
        text: ['�߼�', '�ͼ�'],
        inRange: {
            color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
        }
    },
    series: [{
        name: '���л��ͼ۸�����ͼ',
        type: 'heatmap',
        data: {{ city_type_price|safe }},
        label: {
            show: false
        },
        emphasis: {
            itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
        }
    }]
};
cityTypeChart.setOption(cityTypeOption);

// ����볯��Լ۸��Ӱ������ͼ
var areaDirectChart = echarts.init(document.getElementById('areaDirectHeatmap'));

// ���������Χ��ǩ
var areaLabels = [];
{% for range in area_ranges %}
    areaLabels.push("{{ range.0|floatformat:0 }}~{{ range.1|floatformat:0 }}�O");
{% endfor %}

var areaDirectOption = {
    title: {
        text: '����볯��Է��۵�Ӱ������ͼ',
        left: 'center'
    },
    tooltip: {
        position: 'top'
    },
    grid: {
        height: '70%',
        top: '15%'
    },
    xAxis: {
        type: 'category',
        data: areaLabels,
        splitArea: {
            show: true
        },
        name: '�������',
        nameLocation: 'middle',
        nameGap: 30
    },
    yAxis: {
        type: 'category',
        data: {{ directs|safe }},
        splitArea: {
            show: true
        },
        name: '����',
        nameLocation: 'middle',
        nameGap: 30
    },
    visualMap: {
        min: 0,
        max: 5000,
        calculable: true,
        orient: 'horizontal',
        left: 'center',
        bottom: '5%',
        text: ['�߼�', '�ͼ�'],
        inRange: {
            color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
        }
    },
    series: [{
        name: '�������۸�����ͼ',
        type: 'heatmap',
        data: {{ area_direct_price|safe }},
        label: {
            show: false
        },
        emphasis: {
            itemStyle: {
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
        }
    }]
};
areaDirectChart.setOption(areaDirectOption);

// �����۸��ɢ��ͼ
var areaPriceChart = echarts.init(document.getElementById('areaPriceScatter'));
var areaPriceOption = {
    title: {
        text: '�����۸�Ĺ�ϵɢ��ͼ',
        left: 'center'
    },
    tooltip: {
        trigger: 'item'
    },
    grid: {
        top: '15%'
    },
    xAxis: {
        type: 'value',
        name: '������O��',
        nameLocation: 'middle',
        nameGap: 30
    },
    yAxis: {
        type: 'value',
        name: '�۸�Ԫ��',
        nameLocation: 'middle',
        nameGap: 30
    },
    visualMap: {
        min: 0,
        max: 300,
        dimension: 0,
        orient: 'vertical',
        right: 10,
        top: 'center',
        text: ['�����', '���С'],
        calculable: true,
        inRange: {
            color: ['#50a3ba', '#eac736', '#d94e5d']
        }
    },
    series: [{
        name: '����۸��ϵ',
        type: 'scatter',
        symbolSize: 8,
        data: {{ area_price_data|safe }}
    }]
};
areaPriceChart.setOption(areaPriceOption);

// ��Ӧʽ����ͼ���С
window.addEventListener('resize', function() {
    cityTypeChart.resize();
    areaDirectChart.resize();
    areaPriceChart.resize();
});
</script>
</body>
</html> 