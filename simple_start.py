import os
import webbrowser
import subprocess
import time

def main():
    print("=" * 60)
    print("   ��Դ���ݷ�����۸�Ԥ��ϵͳ - ����������")
    print("=" * 60)
    
    # ��ȡ��ǰĿ¼
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir == "":
        current_dir = os.getcwd()
    
    # ����Django������
    print("���ڳ����������е�Django����...")
    
    # �趨���л���������
    cmd = ["python", "manage.py", "runserver", "8127"]
    
    # ��������
    try:
        if os.name == 'nt':  # Windows
            process = subprocess.Popen(cmd, cwd=current_dir)
        else:  # Linux/Mac
            process = subprocess.Popen(cmd, cwd=current_dir)
        
        # �ȴ�����������
        print("�ȴ�����������...")
        time.sleep(2)
        
        # �������
        print("���ڴ������...")
        webbrowser.open("http://localhost:8127/app/login/")
        
        print("\nϵͳ������!")
        print("=" * 60)
        print("  ���ʵ�ַ: http://localhost:8127/app/login/")
        print("=" * 60)
        print("\n�뱣�ִ˴��ڿ������رմ��ڽ�ֹͣ��������")
        print("��Ctrl+C����ֹͣ���������˳�...")
        
        # �ȴ��û���Ctrl+C
        try:
            process.wait()
        except KeyboardInterrupt:
            pass
        
    except Exception as e:
        print(f"����ʧ��: {e}")
        print("�볢���ֶ�����ϵͳ��")
        input("���س����˳�...")
        return
    
    print("���ڹر�ϵͳ...")
    try:
        process.terminate()
        process.wait(timeout=5)
    except:
        if process:
            process.kill()
    
    print("ϵͳ���˳���")

if __name__ == "__main__":
    main() 