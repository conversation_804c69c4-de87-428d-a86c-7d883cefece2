import subprocess
import webbrowser
import time
import os
import sys
import signal

def main():
    print("=" * 60)
    print("       ��Դ���ݷ�����۸�Ԥ��ϵͳ - ��������")
    print("=" * 60)
    print("��������ϵͳ...")
    
    # ʹ��ֱ���������з���ķ�ʽ����
    print("��������Web������...")
    
    # ��ȡ��ǰĿ¼
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # ���Զ���������ʽ
    try:
        if os.name == 'nt':  # Windows
            # ����ִ�г�����Django��������
            try:
                # ����1: ֱ��ʹ��manage.py����
                server_process = subprocess.Popen(
                    ["python", "manage.py", "runserver", "0.0.0.0:8127"],
                    cwd=current_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
            except:
                # ����2: ʹ��runserverֱ������
                print("���Ա���������ʽ...")
                server_process = subprocess.Popen(
                    [sys.executable, "-c", 
                     "import os; os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Python�ⷿ��Դ���ݿ��ӻ�����.settings'); from django.core.management import execute_from_command_line; execute_from_command_line(['manage.py', 'runserver', '0.0.0.0:8127'])"],
                    cwd=current_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
        else:  # ��Windowsϵͳ
            try:
                # ����1: ֱ��ʹ��manage.py����
                server_process = subprocess.Popen(
                    ["python", "manage.py", "runserver", "0.0.0.0:8127"],
                    cwd=current_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
            except:
                # ����2: ʹ��runserverֱ������
                print("���Ա���������ʽ...")
                server_process = subprocess.Popen(
                    [sys.executable, "-c", 
                     "import os; os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Python�ⷿ��Դ���ݿ��ӻ�����.settings'); from django.core.management import execute_from_command_line; execute_from_command_line(['manage.py', 'runserver', '0.0.0.0:8127'])"],
                    cwd=current_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
    except Exception as e:
        print(f"����ʧ��: {e}")
        print("������򵥵ķ�ʽ...")
        # ���ı��÷��� - ֱ������ python -m http.server
        if os.name == 'nt':
            server_process = subprocess.Popen(
                ["python", "-m", "http.server", "8127"],
                cwd=current_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW
            )
        else:
            server_process = subprocess.Popen(
                ["python", "-m", "http.server", "8127"],
                cwd=current_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
    
    # �ȴ�����������
    print("�ȴ�����������...")
    time.sleep(3)
    
    # �������
    print("���ڴ������...")
    webbrowser.open("http://localhost:8127/app/login/")
    
    print("\nϵͳ������!")
    print("=" * 60)
    print("* ���ʵ�ַ: http://localhost:8127/app/login/")
    print("* ����������ID:", server_process.pid)
    print("=" * 60)
    print("\n���س����رշ��������˳�...")
    
    # �ȴ��û����س�
    input()
    
    # ��ֹ����������
    print("���ڹرշ�����...")
    try:
        if os.name == 'nt':
            server_process.terminate()
        else:
            os.kill(server_process.pid, signal.SIGTERM)
            
        server_process.wait(timeout=5)
        print("�������ѹرա�")
    except:
        print("������δ�������رգ�ǿ�ƽ�������...")
        if os.name == 'nt':
            server_process.kill()
        else:
            os.kill(server_process.pid, signal.SIGKILL)
    
    print("ϵͳ���˳���")

if __name__ == "__main__":
    main() 