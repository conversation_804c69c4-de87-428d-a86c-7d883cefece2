#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
�������ű�
���Python��Django�汾���Լ���Ҫ���������Ƿ��Ѱ�װ
"""

import sys
import platform
import socket
import os
import importlib.util
import subprocess
import traceback

def check_python_version():
    """���Python�汾"""
    print(f"Python�汾: {platform.python_version()}")
    major, minor, _ = platform.python_version_tuple()
    
    if int(major) < 3 or (int(major) == 3 and int(minor) < 6):
        print("����: ����ʹ��Python 3.6����߰汾")
        return False
    return True

def check_django():
    """���Django�Ƿ��Ѱ�װ����汾"""
    try:
        if importlib.util.find_spec("django"):
            import django
            print(f"Django�汾: {django.get_version()}")
            return True
        else:
            print("����: Djangoδ��װ")
            return False
    except Exception as e:
        print(f"���Djangoʱ����: {e}")
        return False

def check_port(port=8127):
    """���˿��Ƿ����"""
    s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    try:
        s.bind(("127.0.0.1", port))
        print(f"�˿�{port}����")
        result = True
    except socket.error:
        print(f"����: �˿�{port}�ѱ�ռ��")
        result = False
    finally:
        s.close()
    return result

def check_required_packages():
    """����Ҫ��Python���Ƿ��Ѱ�װ"""
    required_packages = [
        "django",
        "pandas",
        "numpy",
        "matplotlib",
        "scikit-learn"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        if not importlib.util.find_spec(package):
            missing_packages.append(package)
    
    if missing_packages:
        print(f"����: ���±�Ҫ�İ�δ��װ: {', '.join(missing_packages)}")
        return False
    else:
        print("���б�Ҫ�İ��Ѱ�װ")
        return True

def check_manage_py():
    """���manage.py�ļ��Ƿ����"""
    # ��鵱ǰĿ¼
    if os.path.exists("manage.py"):
        print("�ҵ�manage.py�ļ�")
        return True
    
    # ��鸸Ŀ¼
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if os.path.exists(os.path.join(parent_dir, "manage.py")):
        print("�ڸ�Ŀ¼���ҵ�manage.py�ļ�")
        return True
    
    # �����Ŀ¼
    for root, dirs, files in os.walk(os.path.dirname(os.path.abspath(__file__))):
        if "manage.py" in files:
            print(f"��{root}���ҵ�manage.py�ļ�")
            return True
    
    print("����: �Ҳ���manage.py�ļ�")
    return False

def main():
    """������"""
    print("-" * 40)
    print("��ʼ�������")
    print("-" * 40)
    
    all_checks_passed = True
    
    # ���Python�汾
    if not check_python_version():
        all_checks_passed = False
    
    # ���Django
    if not check_django():
        all_checks_passed = False
    
    # ���˿�
    if not check_port():
        all_checks_passed = False
    
    # ����Ҫ�İ�
    if not check_required_packages():
        all_checks_passed = False
    
    # ���manage.py�ļ�
    if not check_manage_py():
        all_checks_passed = False
    
    print("-" * 40)
    if all_checks_passed:
        print("�������ͨ����")
        return 0
    else:
        print("�������δͨ������������������ٳ�������ϵͳ��")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except Exception as e:
        print(f"�����������з�������: {e}")
        traceback.print_exc()
        sys.exit(1) 