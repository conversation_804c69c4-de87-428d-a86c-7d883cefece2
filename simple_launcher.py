#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
��Դ���ݷ���ϵͳ�����ű�
���ܣ����Ҳ�����Django������
"""

import os
import sys
import subprocess
import signal
import time
import traceback

def find_manage_py():
    """����manage.py�ļ�"""
    # �ڵ�ǰĿ¼����
    if os.path.exists("manage.py"):
        return os.path.abspath("manage.py")
    
    # �ڸ�Ŀ¼����
    parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if os.path.exists(os.path.join(parent_dir, "manage.py")):
        return os.path.join(parent_dir, "manage.py")
    
    # ����Ŀ¼����
    for root, dirs, files in os.walk(os.path.dirname(os.path.abspath(__file__))):
        if "manage.py" in files:
            return os.path.join(root, "manage.py")
    
    return None

def start_django_server():
    """����Django������"""
    manage_py_path = find_manage_py()
    
    if not manage_py_path:
        print("�����Ҳ���manage.py�ļ���")
        print("��ȷ��������ȷ��Ŀ¼�����д˽ű���")
        return False
    
    print(f"�ҵ�manage.py: {manage_py_path}")
    
    # �л���manage.py����Ŀ¼
    os.chdir(os.path.dirname(manage_py_path))
    
    try:
        # ��������Django������
        print("����Django������ (�˿�8127)...")
        cmd = [sys.executable, "manage.py", "runserver", "0.0.0.0:8127"]
        
        # ��Windows�ϣ�ʹ��CREATE_NEW_PROCESS_GROUP��־������Ctrl+C��������
        if os.name == 'nt':
            server_process = subprocess.Popen(cmd, creationflags=subprocess.CREATE_NEW_PROCESS_GROUP)
        else:
            server_process = subprocess.Popen(cmd)
        
        print("Django��������������")
        print("�����: http://localhost:8127")
        print("��Ctrl+Cֹͣ������...")
        
        # �ȴ�����������
        server_process.wait()
        return True
        
    except KeyboardInterrupt:
        print("\n�յ�ֹͣ�źţ����ڹرշ�����...")
        if os.name == 'nt':
            os.kill(server_process.pid, signal.CTRL_BREAK_EVENT)
        else:
            server_process.terminate()
        
        # ������һЩʱ��������
        time.sleep(1)
        return True
        
    except Exception as e:
        print(f"����������ʱ����: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = start_django_server()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n�����û��ж�")
        sys.exit(0)
    except Exception as e:
        print(f"��������: {e}")
        traceback.print_exc()
        sys.exit(1) 