@echo off
chcp 65001 > nul
title ��Դ���ݷ���ϵͳ������

:: ������ɫ
color 0A

echo ============================================================
echo                   ��Դ���ݷ���ϵͳ������
echo ============================================================
echo.

:: ������־Ŀ¼
if not exist logs mkdir logs

:: ��ǰʱ���
for /f "tokens=2 delims==" %%I in ('wmic os get localdatetime /format:list') do set datetime=%%I
set logfile=logs\startup_%datetime:~0,8%_%datetime:~8,6%.log

echo ���Python��װ���...
python --version > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [����] δ�ҵ�Python! �밲װPython 3.6����߰汾�� >> %logfile%
    echo [����] δ�ҵ�Python! �밲װPython 3.6����߰汾��
    echo       ���ص�ַ: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

:: ���Python�汾
for /f "tokens=2" %%V in ('python --version 2^>^&1') do set pyver=%%V
echo ��⵽Python�汾: %pyver% >> %logfile%
echo ��⵽Python�汾: %pyver%

:: ��鲢��װ��Ҫ������
echo.
echo ���ڼ�鲢��װ��Ҫ������...
echo �������... >> %logfile%

:: ������ʱ���ű�
echo import sys > check_deps.py
echo deps = {"django": False, "pandas": False, "numpy": False, "matplotlib": False, "scikit-learn": False} >> check_deps.py
echo try: >> check_deps.py
echo     import django >> check_deps.py
echo     deps["django"] = True >> check_deps.py
echo except ImportError: pass >> check_deps.py
echo try: >> check_deps.py
echo     import pandas >> check_deps.py
echo     deps["pandas"] = True >> check_deps.py
echo except ImportError: pass >> check_deps.py
echo try: >> check_deps.py
echo     import numpy >> check_deps.py
echo     deps["numpy"] = True >> check_deps.py
echo except ImportError: pass >> check_deps.py
echo try: >> check_deps.py
echo     import matplotlib >> check_deps.py
echo     deps["matplotlib"] = True >> check_deps.py
echo except ImportError: pass >> check_deps.py
echo try: >> check_deps.py
echo     import sklearn >> check_deps.py
echo     deps["scikit-learn"] = True >> check_deps.py
echo except ImportError: pass >> check_deps.py
echo for name, installed in deps.items(): >> check_deps.py
echo     if not installed: >> check_deps.py
echo         print(name) >> check_deps.py

:: ��ȡȱʧ������
set missing_deps=
for /f "tokens=*" %%a in ('python check_deps.py') do (
    set missing_deps=!missing_deps! %%a
)

:: ��װȱʧ������
if defined missing_deps (
    echo ���ڰ�װȱʧ������: %missing_deps%
    echo ��װ����: %missing_deps% >> %logfile%
    
    python -m pip install --upgrade pip >> %logfile% 2>&1
    
    for %%d in (%missing_deps%) do (
        echo ���ڰ�װ %%d...
        python -m pip install %%d >> %logfile% 2>&1
        if %ERRORLEVEL% NEQ 0 (
            echo [����] ��װ %%d ʧ�ܣ����ֶ���װ��
            echo [����] ��װ %%d ʧ�� >> %logfile%
        ) else (
            echo %%d ��װ�ɹ���
        )
    )
) else (
    echo ���б�Ҫ�������Ѱ�װ��
)

:: ɾ����ʱ���ű�
del check_deps.py > nul 2>&1

echo.
echo ============================================================
echo               ����������Դ���ݷ���ϵͳ...
echo ============================================================

echo ����ϵͳ... >> %logfile%

:: ����Django������
python improved_launcher.py
set server_status=%ERRORLEVEL%

if %server_status% NEQ 0 (
    echo.
    echo [����] ϵͳ����ʧ�ܣ��������: %server_status%
    echo [����] ϵͳ����ʧ�ܣ��������: %server_status% >> %logfile%
    echo ����logsĿ¼�µ���־�ļ��Ի�ȡ��ϸ������Ϣ��
    echo.
    pause
    exit /b %server_status%
)

pause
exit /b 0 